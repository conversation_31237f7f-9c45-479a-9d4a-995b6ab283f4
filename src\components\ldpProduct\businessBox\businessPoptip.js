/*
 * @Description: 应用状态信息
 * @Author: <PERSON><PERSON>
 * @Date: 2023-04-13 10:11:35
 * @LastEditTime: 2023-07-07 16:13:27
 * @LastEditors: Zale Ying
 */
import './businessPoptip.less';
import { getDashboardConfig } from '@/api/mcApi';
import { instanceInfoDict } from '@/config/exchangeConfig';
export default {
    name: 'businessPoptip',
    props: {
        placement: {
            type: String,
            default: 'right'
        },
        node: {
            type: Object,
            default: function() {
                return {};
            }
        }
    },
    inject: ['getMonitorEvents'],
    data() {
        return {
        };
    },
    mounted() {
    },
    methods: {
        clusterGoLink(url, id, name) {
            const query = {
                instanceId: id,
                tabName: name
            };
            this.$hCore.navigate(url, { history: true }, query);
        },
        getStatusDesc(key) {
            switch (key) {
                case 'runing':
                    return '运行中';

                case 'warning':
                    return '警告';

                case 'exception':
                    return '异常';

                case 'stop':
                    return '停止';

                default:
                    return '未托管';
            }
        },
        // 应用状态墙和应用topo页面字典与核心数据上场业务状态字典不一致
        getBizStatusDesc(key) {
            switch (key) {
                case 1:
                    return '初始状态';
                case 2:
                    return '数据上场中';
                case 3:
                    return '数据上场完成';
                case 4:
                    return '系统就绪';
                case 5:
                    return '数据下场中';
                case 6:
                    return '数据下场完成';
                case 7:
                    return '系统已下线';
                case 17:
                    return '数据上场失败';
                case 18:
                    return '数据下场失败';
                case 19:
                    return '回切中';
                case 20:
                    return '已回切';
                default:
                    return '';
            }
        },
        // 插件状态
        getPluginStatusDesc(key) {
            switch (key) {
                case 'LdpReady':
                    return '就绪';
                case 'LdpUnready':
                    return '未就绪';
                default:
                    return '';
            }
        },
        goLink(url, id, name) {
            const query = {
                instanceId: id
                // tabName: name
            };
            this.$hCore.navigate(url, { history: true }, query);
        },
        // 调用父组件的方法
        callParentMethod(instanceId) {
            this.$refs['poptip'].close();
            this.getMonitorEvents(instanceId);
        },
        async getDashboardConfig(type) {
            let list = [];
            const res = await getDashboardConfig({
                productId: localStorage.getItem('productInstNo'),
                type,
                developPlatform: 'hsldp'
            });
            if (res.code === '200') {
                list = res?.data?.filter(v => v?.visible === true) || [];
            }
            return list;
        }
    },
    // eslint-disable-next-line complexity
    render() {
        const node = this.node;
        const instanceName = node?.target?.resourceName || node?.target?.baseInfo?.instanceName || '-';
        const instanceDesc = this.$store.state.apmDirDesc?.appTypeDictDesc?.[node?.target?.baseInfo?.instanceType] || node?.target?.baseInfo?.instanceType || '-';
        const instanceId = node?.target?.resourceId || '-';
        const baseInfo = node?.target?.baseInfo;
        const instanceInfo = node?.target?.instanceInfo || {};
        const runningInfo = node?.target?.runningInfo;
        const extParams = node?.target?.baseInfo?.extInfos || node?.target?.extInfos;
        const operateInfo = node?.target?.baseInfo?.operateInfo || node?.target?.operateInfo;
        const observations = node?.target?.observations || node?.target?.baseInfo?.observations || [];
        return <h-poptip
            trigger="click"
            ref="poptip"
            placement={this.placement}
            transfer={true}
            customTransferClassName="pop-business"
            title={`${instanceDesc}(${instanceName})`}
            style="width: 100%;">
            {runningInfo?.exceptionTimeDesc || instanceName}
            <div class="pop-content large-pop-content" slot="content">
                {
                    (runningInfo?.status === 'runing' || runningInfo?.status === 'warning' || runningInfo?.status === 'exception') && (operateInfo?.manageApi || operateInfo?.memoryTable || operateInfo?.memoryMdb || operateInfo?.instanceObservation) ? <section>
                        <div class="pop-content-info">
                            <a style="cursor: pointer; width: 100px; text-align: right; display: inline-block; position: absolute; right: 15px; top: 8px;"
                                onClick={() => { this.getMonitorEvents(instanceId); }}>告警：{runningInfo?.alertNumber ?? '-'}</a>
                            <div class="info-title">节点操作</div>
                            <ul>
                                {
                                        operateInfo?.memoryTable
                                            ? <li>
                                                <span>内存数据管理：</span><a onClick={() => this.goLink('/ldpTable', instanceId)}>查看</a>
                                            </li> : ''
                                }
                                {
                                        operateInfo?.memoryMdb
                                            ? <li>
                                                <span>内存数据管理：</span><a onClick={() => this.goLink('/sqlTable', instanceId)}>查看</a>
                                            </li> : ''
                                }
                                {
                                        operateInfo?.manageApi
                                            ? <li>
                                                <span>管理功能：</span><a onClick={() => this.goLink('/managementQuery', instanceId)}>查看</a>
                                            </li> : ''
                                }
                                {
                                    observations.length
                                        ? <li>
                                            <span>实例观测：</span>
                                            {
                                                observations.map((item, index) => {
                                                    return <a
                                                        key={index}
                                                        style={{ marginRight: '5px', whiteSpace: 'nowrap' }}
                                                        onClick={() => this.clusterGoLink('/ldpDataObservation', instanceId, item.name)}>
                                                        {item?.describe}
                                                    </a>;
                                                })
                                            }
                                        </li> : ''
                                }
                            </ul>
                        </div>
                        <br />
                    </section> : ''
                }

                <div class="pop-content-info">
                    <div class="info-title">基础信息</div>
                    <ul>
                        <li>
                            <span>应用节点名：</span>{instanceName}
                        </li>
                        <li>
                            <span>应用节点类型：</span>{instanceDesc}
                        </li>
                        <li>
                            <span>应用节点版本：</span>{baseInfo?.version || '-'}
                        </li>
                        {
                            baseInfo?.developPlatform
                                ? <li>
                                    <span>应用开发平台：</span>{baseInfo.developPlatform}
                                </li> : ''
                        }
                    </ul>
                </div>
                <br />
                <div class="pop-content-info">
                    <div class="info-title">运行状态</div>
                    <ul>
                        <li>
                            <span>主机名：</span>{instanceInfo?.hostNameAlias || '-'}
                        </li>
                        <li>
                            <span>IP地址/域名：</span>{instanceInfo?.ips?.[0] || '-'}
                        </li>
                        <li>
                            <span>管理端口：</span>{instanceInfo?.port || '-'}
                        </li>
                        <li>
                            <span>集群角色：</span>{this.$store.state.apmDirDesc?.appInstanceClusterRoleDict[runningInfo?.clusterRole] || '-'}
                        </li>
                        <li>
                            <span>进程状态：</span>{this.getStatusDesc(runningInfo?.status)}
                        </li>
                        <li>
                            <span>业务状态：</span>{this.getBizStatusDesc(runningInfo?.bizStatus) || '-'}
                        </li>
                        <li>
                            <span>插件状态：</span>{this.getPluginStatusDesc(runningInfo?.pluginStatus) || '-'}
                        </li>
                        <li>
                            <span>最后心跳时间：</span>{runningInfo?.heartbeatTime || '-'}
                        </li>
                    </ul>
                </div>
                {
                    extParams && extParams.length ? <section>
                        <br />

                        {
                            extParams.map((item, idx) => {
                                return <div class="pop-content-info" style="margin-bottom: 10px;">
                                    <div class="info-title">{idx === 0 ? '扩展信息' : ''}</div>
                                    <ul>
                                        {
                                            Object.keys(item).map(key => {
                                                return instanceInfoDict.extInfo?.[key]
                                                    ? <li>
                                                        <span>{instanceInfoDict.extInfo[key]}：</span>{Array.isArray(item[key]) ? item[key].join('；') : item[key] || '-'}
                                                    </li> : '';
                                            })
                                        }
                                    </ul>
                                </div>;
                            })
                        }
                    </section> : ''
                }

            </div>
        </h-poptip>;
    }
};

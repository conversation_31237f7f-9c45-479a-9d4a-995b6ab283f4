<template>
    <div class="grid-box">
        <div ref="wrapper" class="wrapper-box">
            <grid-layout
                :layout.sync="layout"
                :col-num="colNum"
                :row-height="rowHeight"
                :is-draggable="false"
                :is-resizable="false"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[4, 10]"
                :use-css-transforms="true"
                :style="gridItemStyle">
                <grid-item
                    v-for="(item, idx) in layout"
                    :key="item.i"
                    class="service-box"
                    :x="item.x"
                    :y="item.y"
                    :w="item.w"
                    :h="item.h"
                    :i="item.i">
                    <business-box
                        ref="business"
                        :panel="panels[idx]"
                        :type="type"
                        :placement="item.x < colNum/2 ? 'right' : 'left'"
                        :mode="groupMode"/>
                </grid-item>
            </grid-layout>
            <no-data v-if="!panels.length" />
            <a-loading v-if="loading" style="width: 100%; height: 100%;"></a-loading>
        </div>
    </div>
</template>
<script>
import { GridLayout, GridItem } from 'vue-grid-layout';
import noData from '@/components/common/noData/noData';
import { isDivisible, getObjByArray } from '@/utils/utils';
import { getClusterRoles, getInstancesObservablesNode, getMonitorHeartbeats, getClustersObservablesNode } from '@/api/httpApi';
import { getProductObservation } from '@/api/topoApi';
import aLoading from '@/components/common/loading/aLoading';
import businessBox from '@/components/ldpProduct/businessBox/clusterBusinessBox';
import _ from 'lodash';
const roomTypeEnum = {
    1: '主',
    2: '同城',
    3: '异地',
    4: '未知'
};
export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        hiddenArbInstance: {
            type: Boolean,
            default: undefined
        },
        groupMode: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            layout: [],
            panels: [],
            rowHeight: 0,
            colNum: 12,
            layoutStu: true,
            timer: null,
            loading: false
        };
    },
    async mounted() {
        window.addEventListener('resize', this.resetLayout);
    },
    methods: {
        // 初始化
        async init() {
            this.clearPolling();
            this.loading = true;
            this.setPolling();
            const panels = await this.getObservablesDashboards();
            const nodeObj = await this.getObservablesNodes();
            if (!nodeObj || !panels) return;
            this.generateLayout(panels, nodeObj);
            await this.generateStatus();
            this.loading = false;
            this.resetLayout();
        },

        // 重置layout
        resetLayout() {
            this.layoutStu = false;
            this.$nextTick(() => {
                this.rowHeight = parseInt(this.$refs.wrapper?.clientHeight / 10 - 5 * 2, 10);
                this.layoutStu = true;
            });
        },
        // 生成面板
        generatePanels(groupMode, list) {
            const panels = [];
            if (groupMode === 'service') {
                // 应用节点观测仪表盘
                list.forEach(item => {
                    const data = {
                        content: [],
                        id: item.serviceCode,
                        name: item.serviceName
                    };
                    if (Array.isArray(item?.appClusters) && item?.appClusters?.length) {
                        item.appClusters.forEach(ele => {
                            const cluster = {
                                id: ele.id,
                                label: ele.clusterName,
                                nodes: [],
                                target: {
                                    resourceId: ele.id,
                                    resourceNameLeft: `分片${ele.shardingNo || '-'}`,
                                    resourceNameRight: `${this.$store.state?.apmDirDesc?.clusterTypeDict?.[ele?.clusterType?.toUpperCase()]}: ${ele?.memberNumber}`
                                }
                            };
                            if (Array.isArray(ele?.instances) && ele?.instances?.length) {
                                ele.instances.forEach(instance => {
                                    const node = {
                                        id: instance.id,
                                        label: instance.instanceName,
                                        target: {
                                            resourceId: instance.id,
                                            resourceName: instance.instanceName,
                                            resourceType: instance.instanceType,
                                            instanceInfo: instance,
                                            baseInfo: {},
                                            runningInfo: {},
                                            operateInfo: {},
                                            observations: []
                                        }
                                    };
                                    cluster.nodes.push(node);
                                });
                            }
                            data.content.push(cluster);
                        });
                    }
                    panels.push(data);
                });
            } else if (groupMode === 'deploy') {
                list.forEach(item => {
                    const data = {
                        content: [],
                        id: item.roomId,
                        name: item.roomNameAlias || item.roomName,
                        sign: roomTypeEnum?.[item.roomType]
                    };
                    if (Array.isArray(item?.hosts) && item?.hosts?.length) {
                        item.hosts.forEach(ele => {
                            const cluster = {
                                id: ele.id,
                                label: ele.hostNameAlias || '-',
                                nodes: [],
                                target: {
                                    resourceId: ele.id,
                                    resourceNameLeft: `${ele.hostNameAlias || '-'}`,
                                    resourceNameRight: `${ele.ips?.[0] || '-'}`
                                }
                            };
                            if (Array.isArray(ele?.instances) && ele?.instances?.length) {
                                ele.instances.forEach(instance => {
                                    const node = {
                                        id: instance.id,
                                        label: instance.instanceName,
                                        target: {
                                            resourceId: instance.id,
                                            resourceName: instance.instanceName,
                                            resourceType: instance.instanceType,
                                            instanceInfo: instance,
                                            baseInfo: {},
                                            runningInfo: {},
                                            operateInfo: {},
                                            observations: []
                                        }
                                    };
                                    cluster.nodes.push(node);
                                });
                            }
                            data.content.push(cluster);
                        });
                    }
                    panels.push(data);
                });
            }
            return panels;
        },
        // 获取模版接口
        getObservablesDashboards() {
            // 应用节点观测仪表盘
            const param = {
                productId: this.productInstNo,
                observationMode: this.groupMode,
                hiddenArbInstance: this.hiddenArbInstance
            };
            return new Promise((resolve, reject) => {
                getProductObservation(param).then(res => {
                    let panels = [];
                    if (res.success) {
                        panels = this.generatePanels(this.groupMode, res?.data?.services || res?.data?.rooms || []);
                        resolve(panels);
                    } else {
                        resolve(false);
                    }
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 生成模型
        async generateLayout(panels, nodeObj) {
            this.colNum = isDivisible(panels.length);
            const w = this.colNum / panels.length;
            this.layout = [];
            this.panels = [];
            panels.forEach((item, index) => {
                this.layout.push({
                    x: index * w,
                    y: 0,
                    w,
                    h: 10,
                    i: item.id.toString()
                });
                this.panels.push({
                    ...item
                });
            });

            // 弹窗静态信息
            this.panels.forEach(item => {
                Array.isArray(item.content) && item.content.forEach((element) => {
                    Array.isArray(element.nodes) && element.nodes.forEach(ele => {
                        ele.target.baseInfo = { ...nodeObj[ele?.target?.resourceId] };
                        ele.target.runningInfo = { ...nodeObj[ele?.target?.resourceId]?.runningInfo };
                        ele.target.operateInfo = { ...nodeObj[ele?.target?.resourceId]?.operateInfo };
                        ele.target.observations = nodeObj[ele?.target?.resourceId]?.observations || [];
                    });
                });
            });
        },
        // 集群信息
        getClustersObservablesNode(){
            const param = {
                productId: this.productInstNo
            };
            return new Promise((resolve, reject) => {
                getClustersObservablesNode(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'id');
                        resolve(data);
                    } else {
                        resolve(false);
                    };
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 分组信息
        getInstancesObservablesNode(){
            const param = {
                productId: this.productInstNo
            };
            return new Promise((resolve, reject) => {
                getInstancesObservablesNode(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'id');
                        resolve(data);
                    } else {
                        resolve(false);
                    };
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 获取心跳数据
        getMonitorHeartbeats() {
            const param = {
                productId: this.productInstNo,
                type: this.type
            };
            return new Promise((resolve, reject) => {
                getMonitorHeartbeats(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'id');
                    }
                    resolve(data);
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 获取集群角色
        getClusterRoles() {
            const param = {
                productId: this.productInstNo
            };
            return new Promise((resolve, reject) => {
                getClusterRoles(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'instanceId');
                    }
                    resolve(data);
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 往模型中加入节点状态、集群信息、分组信息
        async generateStatus() {
            const appStus = await this.getMonitorHeartbeats();
            const getClusterRoles = await this.getClusterRoles();
            const panels = _.cloneDeep(this.panels || []);
            for (const panel of Object.values(panels)) {
                for (const element of Object.values(panel.content || [])) {
                    for (const ele of Object.values(element.nodes || [])) {
                        ele.target.runningInfo = {
                            ...(ele.target.runningInfo || {}),
                            ...(appStus[ele?.target.resourceId] || {}),
                            clusterRole: getClusterRoles[ele?.target?.resourceId]?.clusterRole || ''
                        };
                        // 集群成员--集群角色
                        if (this.type === 'cluster' && ele?.target?.baseInfo?.members?.length){
                            for (const member of  Object.values(ele?.target?.baseInfo?.members || [])) {
                                member.clusterRole = getClusterRoles[member.id]?.clusterRole || '-';
                            }
                        }
                    }
                }
            }
            this.panels = [...panels];
        },
        // 应用集群信息列表查询
        async getObservablesNodes() {
            if (this.type === 'instance') {
                const res = await this.getInstancesObservablesNode();
                return res || [];
            } else if (this.type === 'cluster') {
                const res = await this.getClustersObservablesNode();
                return res || [];
            }
        },
        // 定时器
        setPolling(){
            this.clearPolling();
            this.timer = setInterval(async () => {
                await this.generateStatus();
            }, 10000);
        },
        clearPolling(){
            this.timer && clearInterval(this.timer);
        }
    },
    components: { GridLayout, GridItem, businessBox, aLoading, noData },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
        window.removeEventListener('resize', this.resetLayout);
    },
    computed: {
        gridItemStyle() {
            return { minWidth: this.panels.length * 170 + 'px' };
        }
    }
};
</script>
<style lang="less" scoped>
.grid-box {
    width: 100%;
    height: 100%;

    /deep/ .h-radio-wrapper:first-child {
        border: 0;
    }

    .wrapper-box {
        width: 100%;
        height: 100%;
        position: relative;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .service-box {
        padding-right: 3px;
        border-right: 1px solid #9797970e;
    }

    .app-grid-title {
        padding-left: 15px;
        color: #fff;
        font-size: 14px;
    }

    .app-grid-title::before {
        display: inline-block;
        position: relative;
        left: -10px;
        top: 2px;
        content: "";
        width: 4px;
        height: 15px;
        background: var(--link-color);
    }
}
</style>

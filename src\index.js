/*
 * @Description:
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-01 16:23:30
 * @LastEditTime: 2023-05-05 11:02:07
 * @LastEditors: <PERSON><PERSON>
 */
import hCore, { store } from 'hui-core';
import './reset.css';
import './color.css';
import hui from 'h_ui';
import Table from '@hui/table';
import '@hui/table/dist/table.css';
import 'h_ui/dist/h_ui.min.css';
import '@/assets/css/main.less';
import routes from './router/router';
import Vue from 'vue';
import { importFontFace } from '@/utils/utils.js';
import GlobalMethods from '@/global.js'; // 引入全局变量、全局方法文件

Vue.use(hui);
Vue.use(Table);

// 使用全局变量和全局方法
Vue.use(GlobalMethods);

Vue.config.productionTip = false;

const app = hCore({
    extraModelOptions: {
        store
    }
});

app.addRoutes(routes);

app.start((vue) => {
    importFontFace(vue);
});

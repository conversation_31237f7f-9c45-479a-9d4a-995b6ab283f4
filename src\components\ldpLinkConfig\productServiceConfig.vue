<template>
    <div class="tab-box">
        <div ref="table-box" class="table-box">
            <obs-table
                :loading="tableLoading"
                :title="tableTitle"
                :tableData="tableData"
                :columns="columns"
                highlightRow
                rowSelectOnly
                :height="tableHeight"
                showTitle
                @on-current-change="tableRowcheckedChange"
            />
        </div>
        <h-tabs
            v-if="filteredTabs.length"
            v-model="activeTab"
            :animated="false"
            class="table-box"
            @on-click="handleTabsData">
            <!-- 显示数据分片、数据管理功能号、平台功能号、业务功能号标签 -->
            <h-tab-pane
                v-for="tab in filteredTabs"
                :key="tab.name"
                :label="tab.label"
                :name="tab.name">
                <obs-table
                    :loading="tabLoading"
                    :isSimpleTable="tab.isSimpleTable"
                    :tableData="tab.tableData"
                    :columns="tab.columns"
                    :height="tableHeight"
                    showTitle
                />
            </h-tab-pane>
            <a-button
                v-show="currentRow.identity === 'bizproc'"
                slot="extra"
                type="dark"
                :loading="syncloading"
                class="sync-btn"
                @click="syncDataConfig">同步服务数据
            </a-button>
        </h-tabs>
        <no-data v-else class="table-box" text="暂无数据" />
        <a-loading v-if="loading"></a-loading>

        <!-- 分片表抽屉 -->
        <sharding-table-detail-drawer
            v-if="shardingTableInfo.status"
            :modalInfo="shardingTableInfo"
        />
        <!-- 功能号修改 -->
        <edit-fuction-no-modal
            v-if="editFuctionNoInfo.status"
            :modalInfo="editFuctionNoInfo"
            @save="saveFuncNoConfig"
        />
        <!-- 同步结果 -->
        <unified-modal
            v-if="syncResInfo.status"
            :modalInfo="syncResInfo"
            @modal-confirm="goLink"
        />

        <!-- 应用服务识别规则弹窗 -->
        <service-rule-setting-drawer
            v-if="serviceSetting.status"
            :productId="productInfo.id"
            :modalInfo="serviceSetting"
            @update="initData">
        </service-rule-setting-drawer>
    </div>
</template>

<script>
import {
    getBusinessServices, syncDataConfig, saveFuncNoConfig,
    getServiceDataSharding, getServiceDataManagers, getBusFunctionNumber, getLdpManageApis,
    getApplicationRegister, getArbitrationCenter, getConfigurationCenter
} from '@/api/productApi'; // 引入API请求
import obsTable from '@/components/common/obsTable/obsTable'; // 引入 obsTable 组件
import aLoading from '@/components/common/loading/aLoading'; // 引入加载组件
import noData from '@/components/common/noData/noData';
import aButton from '@/components/common/button/aButton'; // 引入按钮组件
import unifiedModal from '@/components/common/apmMsgBox/unifiedModal'; // 接口结果反馈
import { transferVal } from '@/utils/utils'; // 引入工具方法
import shardingTableDetailDrawer from '@/components/ldpLinkConfig/modal/shardingTableDetailDrawer.vue'; // 引入分片表抽屉组件
import editFuctionNoModal from '@/components/ldpLinkConfig/modal/editFuctionNoModal.vue'; // 功能号修改
import serviceRuleSettingDrawer from '@/components/ldpLinkConfig/modal/serviceRuleSettingDrawer.vue';
import { initializeConfigColumns, initializeLdpFuncColumns, initializeBusFuncColumns,
    initializeApplicationColumns, initializeArbitrationColumns, initializeConfigurationColumns
} from '@/components/ldpLinkConfig/constant.js';

export default {
    name: 'ProductServiceConfig', // 组件名称
    components: {
        obsTable, aLoading, aButton, noData,
        shardingTableDetailDrawer,
        editFuctionNoModal,
        serviceRuleSettingDrawer,
        unifiedModal
    }, // 注册组件
    props: {
        productInfo: {
            type: Object, // 属性类型为对象
            default: () => ({}) // 默认值为空对象
        }
    },
    data() {
        return {
            loading: false, // 全局加载状态
            tabLoading: false, // 标签加载状态
            tableLoading: false, // 表格加载状态
            syncloading: false, // 同步按钮
            tableHeight: 0, // 表格高度
            tableTitle: {
                label: '业务服务', // 表格标题
                slots: [] // 空的槽插入
            },
            columns: this.initializeColumns(), // 初始化列配置
            tableData: [], // 表格数据
            currentRow: {}, // 当前选中行数据
            // 定义所有的tab数据
            tabList: [
                {
                    label: '数据分片',
                    name: 'dataSharding',
                    columns: initializeConfigColumns,
                    tableData: [],
                    apiName: getServiceDataSharding
                },
                {
                    label: '数据管理',
                    name: 'dataManage',
                    columns: this.initializeClusterColumns(),
                    tableData: [],
                    apiName: getServiceDataManagers
                },
                {
                    label: '平台功能号',
                    name: 'ldpFunction',
                    isSimpleTable: true,
                    columns: initializeLdpFuncColumns,
                    tableData: [],
                    apiName: getLdpManageApis
                },
                {
                    label: '业务功能号',
                    name: 'busFunction',
                    isSimpleTable: true,
                    columns: initializeBusFuncColumns,
                    tableData: [],
                    apiName: getBusFunctionNumber
                },
                {
                    label: '应用注册',
                    name: 'applicationRegister',
                    columns: initializeApplicationColumns,
                    tableData: [],
                    apiName: getApplicationRegister
                },
                {
                    label: '仲裁中心',
                    name: 'arbitrationCenter',
                    columns: initializeArbitrationColumns,
                    tableData: [],
                    apiName: getArbitrationCenter
                },
                {
                    label: '配置中心',
                    name: 'configurationCenter',
                    columns: initializeConfigurationColumns,
                    tableData: [],
                    apiName: getConfigurationCenter
                }
            ],
            activeTab: 'dataSharding',

            shardingTableInfo: {
                status: false // 分片表抽屉状态
            },
            editFuctionNoInfo: {
                status: false // 功能号
            },
            syncResInfo: {
                status: false, // 同步结果弹窗
                contentType: 'text',
                useHeaderSlot: true,
                configKey: ''
            },
            // 服务规则配置弹窗
            serviceSetting: {
                status: false
            }
        };
    },
    computed: {
        /**
         * 过滤选项卡列表以确定当前显示的选项卡集合。
         *
         * @returns {Array} 返回过滤后的选项卡列表，根据特定条件筛选。
         *
         */
        filteredTabs() {
            return this.tabList.filter(tab => {
                if (['dataSharding', 'dataManage'].includes(tab.name)) {
                    return this.currentRow.identity === 'bizproc';
                } else if (['ldpFunction', 'busFunction'].includes(tab.name)) {
                    return (this.currentRow.developPlatforms || []).includes('hsldp');
                } else if (['applicationRegister', 'arbitrationCenter', 'configurationCenter'].includes(tab.name)) {
                    return this.currentRow.identity === 'zookeeper' || (this.currentRow.developPlatforms || []).includes('apache');
                }
                return true;
            });
        }
    },
    mounted() {
        this.setTableHeight(); // 设置表格高度
        window.addEventListener('resize', this.setTableHeight); // 监听窗口变化
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.setTableHeight); // 移除窗口变化监听
    },

    methods: {
        /**
         * 初始化业务服务列配置
         */
        initializeColumns() {
            return [
                { title: '服务名', key: 'serviceName', ellipsis: true },
                { title: '集群应用节点类型', key: 'instanceTypes', ellipsis: true,
                    render: (h, params) => {
                        const data = (params.row?.instanceTypes || [])
                            .map(type => this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[type] || type)
                            .join('，');
                        return h('span', data || '-');
                    }
                },
                {
                    title: '数据分片',
                    key: 'shardingNumber',
                    ellipsis: true,
                    formatMethod: (row) => row?.shardingNumber ?? '-'
                }
            ];
        },
        // eslint-disable-next-line max-params
        renderFuncNo(h, params, key, saveKey, canEdit = true) {
            const content = [];

            // 添加主要内容和提示
            if (params?.row?.[key + 'Desc']) {
                content.push(
                    h('h-poptip', {
                        props: {
                            autoPlacement: true,
                            trigger: 'hover',
                            customTransferClassName: 'apm-poptip monitor-poptip'
                        }
                    }, [
                        h('span', params?.row?.[key]  ?? '-'),
                        h('div', {
                            slot: 'content',
                            class: 'pop-content',
                            style: { 'white-space': 'normal' }
                        }, params?.row?.[key + 'Desc'] || '-')
                    ])
                );
            } else {
                content.push(h('span', params?.row?.[key] ?? '-'));
            }

            // 添加编辑图标
            if (canEdit) {
                content.push(
                    h('h-icon', {
                        props: {
                            name: 't-b-modify'
                        },
                        style: {
                            color: '#2D8DE5',
                            cursor: 'pointer',
                            marginLeft: '10px'
                        },
                        on: {
                            'on-click': () => this.handleIconClick(saveKey, params?.row, params?.column)
                        }
                    })
                );
            }

            return h('div', content);
        },
        /**
         * 初始化数据管理列配置
         */
        initializeClusterColumns() {
            // 初始化数据管理列配置
            return [
                {
                    title: '集群应用节点类型',
                    key: 'instanceType',
                    ellipsis: true,
                    render: (h, params) => {
                        const data = this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[params?.row?.instanceType] || params?.row?.instanceType || '-';
                        return h('span', data || '-');
                    }
                },
                {
                    title: '集群',
                    key: 'clusterName',
                    ellipsis: true,
                    formatMethod: (row) => row?.clusterName ?? '-'
                },
                {
                    title: '数据上场功能号',
                    key: 'dataInitialImportFuncNo',
                    ellipsis: true,
                    render: (h, params) => this.renderFuncNo(h, params, 'dataInitialImportFuncNo', 'dataInitialImport')
                },
                {
                    title: '二次上场功能号',
                    key: 'dataReimportFuncNo',
                    ellipsis: true,
                    render: (h, params) => this.renderFuncNo(h, params, 'dataReimportFuncNo', 'dataReimport')
                },
                {
                    title: 'SQL功能号',
                    key: 'mdbSqlFuncNo',
                    ellipsis: true,
                    render: (h, params) => this.renderFuncNo(h, params, 'mdbSqlFuncNo', 'mdbSql', false)
                },
                {
                    title: '内存表',
                    key: 'tableNumber',
                    render: (h, params) =>
                        params.row?.tableNumber
                            ? h(
                                'span',
                                {
                                    style: {
                                        color: '#2D8DE5',
                                        cursor: 'pointer'
                                    },
                                    on: {
                                        click: () => this.handleShardingTableClick(params?.row)
                                    }
                                },
                                params.row?.tableNumber
                            )
                            : h('span', transferVal(params.row?.tableNumber) || '-')
                }
            ];
        },

        // 设置表格高度方法
        setTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 80;
        },
        // 初始化数据
        async initData() {
            this.clearPageData(); // 清空页面数据
            this.loading = true;
            try {
                await this.getBusinessServices(); // 获取业务服务数据
            } finally {
                this.loading = false;
            }
        },
        // 清空页面数据
        clearPageData() {
            this.currentRow = {};
            this.tableData = [];

            // 所有tab中表格数据清空
            this.tabList.forEach(item => {
                item.tableData = [];
            });
        },
        // 获取业务服务
        async getBusinessServices() {
            this.tableLoading = true;
            const param = { productId: this.productInfo.productInstNo };
            try {
                const res = await getBusinessServices(param);

                if (res?.code === '200') {
                    this.tableData = res?.data || [];
                    const row = this.tableData.find((row) => row?.shardingNumber) ||
                        this.tableData?.[0] || {};
                    this.tableRowcheckedChange(row);
                }
            } catch (e) {
                console.error('Failed to get business services:', e);
            } finally {
                this.tableLoading = false;
            }
        },
        // 表格选中行变化处理
        tableRowcheckedChange(currentRow) {
            // 所有tab中表格数据清空
            this.tabList.forEach(item => {
                item.tableData = [];
            });
            this.currentRow = currentRow;
            this.tableData.forEach((item) => {
                item._highlight = item?.serviceCode &&
                    item?.serviceCode === this.currentRow?.serviceCode; // 表格刷新后默认选中
            });

            // 默认选中tab标签选项卡
            this.$nextTick(() => {
                const tabList = this.filteredTabs.map(o => o.name);
                this.activeTab = tabList.includes(this.activeTab) ? this.activeTab : tabList?.[0];
                this.handleTabsData();
            });
        },

        /**
         * tab标签数据请求处理
         */
        async handleTabsData(tabName = this.activeTab) {
            this.tabLoading = true;
            try {
                const param = {
                    productId: this.productInfo.productInstNo,
                    shardingNos: this.currentRow?.shardingNos || [],
                    instanceTypes: this.currentRow?.instanceTypes || []
                };

                // 获取当前 tab 的配置项
                const tabConfig = this.tabList.find(tab => tab.name === tabName);
                if (!tabConfig) {
                    return;
                }

                // 发起请求获取数据
                const res = await tabConfig.apiName(param);
                if (res?.code === '200') {
                    tabConfig.tableData = res.data || [];
                } else if (res.code.length === 8) {
                    this.$hMessage.error(res.message);
                }

            } catch (e) {
                console.error(e);
            } finally {
                this.tabLoading = false;
            }
        },
        // 同步数据配置
        async syncDataConfig() {
            this.syncloading = true;
            const param = {
                productId: this.productInfo.productInstNo,
                shardingNos: this.currentRow?.shardingNos || [],
                instanceTypes: this.currentRow?.instanceTypes || []
            };
            try {
                const res = await syncDataConfig(param);
                if (res?.code === '200') {
                    this.syncResInfo = {
                        ...this.syncResInfo,
                        status: true,
                        type: 'success',
                        configKey: '',
                        title: '配置信息同步成功！',
                        contentText: '数据分片、数据管理配置信息已同步！'
                    };
                } else if (res?.code?.length === 8 && res?.code === '02000001') {
                    this.syncResInfo = {
                        ...this.syncResInfo,
                        status: true,
                        type: 'error',
                        title: '配置信息同步失败！',
                        configKey: '去配置',
                        contentText: '数据分片、数据管理配置信息同步失败，请至"产品节点管理-产品服务配置-产品服务网关"配置MDB-SQL接入网关信息。'
                    };
                } else if (res?.code?.length === 8 && res?.code !== '02000001') {
                    this.syncResInfo = {
                        ...this.syncResInfo,
                        status: true,
                        type: 'error',
                        title: '配置信息同步失败！',
                        configKey: '',
                        contentText: res?.message || '配置信息同步失败！'
                    };
                }
                this.handleTabsData();
            } catch (e) {
                console.error('Failed to synchronize tab configuration:', e);
                this.syncResInfo.status = false;
            } finally {
                this.syncloading = false;
            }
        },
        // 分片表点击处理
        handleShardingTableClick(row) {
            this.shardingTableInfo = {
                status: true,
                productId: this.productInfo?.productInstNo,
                clusterId: row?.clusterId || '',
                instanceType: row?.instanceType || ''
            };
        },
        // 修改功能号弹窗
        handleIconClick(key, row, column) {
            this.editFuctionNoInfo = {
                status: true,
                productId: this.productInfo?.productInstNo,
                functionCode: key,
                title: column?.title,
                functionNo: row?.[key + 'FuncNo'],
                instanceType: row?.instanceType
            };
        },
        // 配置服务规则弹窗
        handleServiceSetting() {
            this.serviceSetting = {
                status: true,
                rules: this.tableData
            };
        },
        // 保存配置
        async saveFuncNoConfig(params){
            try {
                const res =  await saveFuncNoConfig(params);
                if (res?.code === '200') {
                    this.$hMessage.success('修改成功！');
                    this.handleTabsData();
                } else if (res?.code?.length === 8) {
                    this.$hMessageSafe.error(res?.message);
                }
            } catch (e){
                console.error('Failed to save:', e);
            }
        },
        goLink(){
            this.$hCore.navigate(`/productServiceList`, {
                menuId: 'mdbAccess'
            });
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less"); // 引入外部含有表格样式的less文件

.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 18px);

    .table-box {
        background: #262d43;
        margin-top: 10px;
        height: calc(50% - 4px);
        padding: 0;

        .obs-table,
        .h-tabs-content-wrap {
            height: auto;
        }
    }

    .sync-btn {
        margin: 0;
    }

    .link-text {
        color: var(--link-color);
        cursor: pointer;
    }

    /deep/ .h-tabs-nav-wrap {
        float: none !important;
    }

    /deep/ .h-tabs-nav-right {
        position: absolute;
        right: 0;
        top: 5px;
    }
}
</style>

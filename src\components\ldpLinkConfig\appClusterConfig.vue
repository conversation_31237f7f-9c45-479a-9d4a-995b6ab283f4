<!-- 应用集群信息Tab -->
<template>
    <div class="tab-box">
        <description-bar :data="baseInfo"></description-bar>
        <div ref="table-box" class="table-box">
            <obs-table :title="tableTitle" :tableData="tableData" :columns="clusterColumns"
                :maxHeight="tableHeight" />
        </div>
        <a-loading v-if="loading"></a-loading>
    </div>
</template>

<script>
import { getProductClusters } from '@/api/productApi';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import descriptionBar from '@/components/common/description/descriptionBar';
export default {
    name: 'AppClusterConfig',
    components: {  obsTable, aLoading, descriptionBar },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            tableHeight: 0,
            baseInfo: {
                title: {
                    label: '仲裁服务信息'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '服务提供者',
                                key: 'configSourceType',
                                span: 8
                            },
                            {
                                label: '服务集群地址',
                                key: 'zkAddr',
                                span: 8
                            },
                            {
                                label: '集中仲裁服务路径',
                                key: 'paths',
                                span: 8
                            }
                        ],
                        data: {
                            configSourceType: '',
                            zkAddr: '',
                            paths: ''
                        }
                    }
                ]
            },
            tableTitle: {
                label: `应用集群信息`
            },
            clusterColumns: [
                {
                    title: '集群名称',
                    key: 'clusterName'
                },
                {
                    title: '集群类型',
                    key: 'clusterType',
                    render: (h, params) => {
                        return h(
                            'div',
                            this.$store?.state?.apmDirDesc?.clusterTypeDict?.[params.row.clusterType] || params.row.clusterType
                        );
                    }
                },
                {
                    title: '集群应用节点类型',
                    key: 'clusterInstanceType',
                    render: (h, params) => {
                        return h(
                            'div',
                            this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[params.row.clusterInstanceType] || params.row.clusterInstanceType
                        );
                    }
                },
                {
                    title: '集群内成员个数',
                    key: 'memberNumber'
                }
            ],
            tableData: [],
            loading: false
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 62;
        },
        async initData() {
            this.clearPageData();
            this.loading = true;
            try {
                await this.getProductClusters();
            } finally {
                this.tableTitle.label = `应用集群信息：${(this.tableData || []).length}`;
                this.loading = false;
            }
        },
        // 清空页面数据
        clearPageData() {
            this.baseInfo.details[0].data = {};
            this.tableData = [];
        },
        // 获取应用集群信息
        async getProductClusters() {
            const res = await getProductClusters({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.baseInfo.details[0].data = {
                    configSourceType: res?.data?.configSourceType,
                    zkAddr: res?.data?.configInfo?.zkAddr,
                    paths: res?.data?.clusterArbAddress
                };
                this.tableData = res?.data?.appClusters;
            }
        }
    }
};
</script>

<style lang="less" scoped>
.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 15px);

    .table-box {
        height: calc(100% - 102px);
    }
}
</style>

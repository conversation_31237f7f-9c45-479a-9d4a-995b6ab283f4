<!-- 应用节点信息Tab -->
<template>
    <div class="tab-box">
        <description-bar
            :data="baseInfo"
            @button-click="handleClick">
        </description-bar>

        <div ref="table-box" class="table-box">
            <obs-table
                :title="tableTitle"
                :tableData="tableData"
                :columns="columns"
                :maxHeight="tableHeight" />
        </div>
        <a-loading v-if="loading"></a-loading>

        <!-- 应用节点同步提示弹窗 -->
        <ins-sync-prompt-modal
            v-if="identifyAppIns.status"
            :modalInfo="identifyAppIns"
            :productInfo="productInfo"
            @view-detail="viewInsSyncDetail"
            @update="initData">
        </ins-sync-prompt-modal>

        <!-- 应用节点同步详情弹窗 -->
        <ins-sync-detail-modal
            v-if="insSyncDetial.status"
            :modalInfo="insSyncDetial"
            :productInfo="productInfo"
            @update="initData">
        </ins-sync-detail-modal>

        <!-- zk信息认证修改弹窗 -->
        <zk-auth-modal
             v-if="zkAuthModalInfo.status"
            :modalInfo="zkAuthModalInfo"
            @close="closeModal"
        />
    </div>
</template>

<script>
import { delManageInstance } from '@/api/httpApi';
import { getProductInstances } from '@/api/productApi';
import obsTable from '@/components/common/obsTable/obsTable';
import aLoading from '@/components/common/loading/aLoading';
import descriptionBar from '@/components/common/description/descriptionBar';
import zkAuthModal from '@/components/ldpLinkConfig/modal/zkAuthModal.vue';
import insSyncPromptModal from '@/components/ldpLinkConfig/modal/insSyncPromptModal.vue';
import insSyncDetailModal from '@/components/ldpLinkConfig/modal/insSyncDetailModal.vue';

export default {
    name: 'AppInstanceConfig',
    components: { obsTable, aLoading, descriptionBar, zkAuthModal, insSyncPromptModal, insSyncDetailModal },
    props: {
        productInfo: {
            type: Object,
            default: () => {}
        }
    },
    data() {
        return {
            tableHeight: 0,
            baseInfo: {
                title: {
                    label: '应用注册中心'
                },
                details: [
                    {
                        dataDic: [
                            {
                                label: '服务提供者',
                                key: 'configSourceType',
                                span: 8
                            },
                            {
                                label: '服务集群地址',
                                key: 'zkAddr',
                                span: 8,
                                slots: [
                                    {
                                        type: 'button',
                                        key: 'zk-auth',
                                        iconName: 't-b-passwordreset'
                                    }
                                ]
                            },
                            {
                                label: '应用节点注册中心路径',
                                key: 'paths',
                                span: 8
                            }
                        ],
                        data: {
                            configSourceType: '',
                            zkAddr: '',
                            paths: ''
                        }
                    }
                ]
            },
            tableTitle: {
                label: `应用节点信息`
            },
            columns: [
                {
                    title: '应用节点名',
                    key: 'instanceName',
                    minWidth: 120
                },
                {
                    title: '应用节点类型',
                    key: 'instanceType',
                    render: (h, params) => {
                        return h(
                            'div',
                            this.$store?.state?.apmDirDesc?.appTypeDictDesc?.[params.row.instanceType] ||
                             params.row.instanceType
                        );
                    },
                    minWidth: 120
                },
                {
                    title: '短应用名',
                    key: 'instanceShortName',
                    minWidth: 120
                },
                {
                    title: '节点编号',
                    key: 'instanceNo'
                },
                {
                    title: '分片编号',
                    key: 'shardingNo'
                },
                {
                    title: '应用节点身份',
                    key: 'instanceIdentities',
                    minWidth: 120,
                    render: (h, params) => {
                        return h(
                            'span', params.row?.instanceIdentities?.map(item => {
                                return this.$store?.state?.apmDirDesc?.instanceIdentityDict?.[item];
                            })?.join(',')
                        );
                    }
                },
                {
                    title: '开发平台',
                    key: 'developPlatform'
                },
                {
                    title: '管理IP',
                    key: 'ip',
                    minWidth: 110
                },
                {
                    title: '管理端口',
                    key: 'manageProxyPort'
                },
                {
                    title: 'zk配置节点',
                    key: 'zkConfigNode',
                    minWidth: 130
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 80,
                    fixed: 'right',
                    render: (h, params) => {
                        return h('div', [
                            h(
                                'Button',
                                {
                                    props: {
                                        type: 'text',
                                        size: 'small'
                                    },
                                    style: {
                                        padding: 0
                                    },
                                    on: {
                                        click: () => {
                                            this.delManageInstance(params.row.id, params.row.instanceName);
                                        }
                                    }
                                },
                                '删除'
                            )
                        ]);
                    }
                }
            ],
            tableData: [],
            configInfo: {},
            appInstanceInfo: {
                type: '',
                productId: '',
                productType: '',
                status: false,
                id: '',
                instanceName: '',
                instanceType: '',
                instanceIdentity: '',
                developPlatform: '',
                ip: '',
                manageProxyPort: ''
            },
            identifyAppIns: {
                status: false
            },
            insSyncDetial: {
                status: false
            },
            loading: false,
            zkUserName: null,
            zkAuthModalInfo: {
                status: false,
                productId: this.productInfo.productId
            }
        };
    },
    mounted() {
        this.fetTableHeight();
        window.addEventListener('resize', this.fetTableHeight);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
    },
    methods: {
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 62;
        },
        async initData() {
            this.clearPageData();
            this.loading = true;
            try {
                await this.getProductInstances();
            } finally {
                this.tableTitle.label = `应用节点信息：${(this.tableData || []).length}`;
                this.loading = false;
            }
        },
        closeModal() {
            this.zkAuthModalInfo = {
                status: false
            };
            this.initData();
        },
        // 清空页面数据
        clearPageData() {
            this.baseInfo.details[0].data = {};
            this.configInfo = {};
            this.tableData = [];
        },
        // 获取应用节点列表
        async getProductInstances() {
            const res = await getProductInstances({ productId: this.productInfo.id });
            if (res.code === '200') {
                this.configInfo = res?.data?.configInfo;
                this.baseInfo.details[0].data = {
                    configSourceType: res?.data?.configSourceType,
                    zkAddr: res?.data?.configInfo?.zkAddr,
                    paths: res?.data?.instanceRegistryAddress
                };
                this.zkUserName = res?.data?.configInfo?.zkUserName ?? null;
                this.tableData = res?.data?.instances;
            }
        },
        // 自动识别应用实例节点
        handleClick(key) {
            if (key === 'zk-auth') {
                this.zkAuthModalInfo = {
                    status: true,
                    productInfo: this.productInfo,
                    zkUserName: this.zkUserName
                    // zkPassword: this.zkPassword
                };
                return;
            }

            // 节点识别与同步
            this.handleInsSyncPrompt();
        },
        // 节点识别同步提示弹窗
        handleInsSyncPrompt() {
            if ((!this.configInfo?.zkAddr) || (!this.configInfo?.paths)) {
                return this.$hMessage.error('无注册中心配置，不支持节点信息同步');
            }
            this.identifyAppIns = {
                status: true
            };
        },
        // 查看节点识别同步详情弹窗
        viewInsSyncDetail(data) {
            this.insSyncDetial = {
                status: true,
                insStatistics: data || {}
            };
        },
        // 删除应用节点实例
        async delManageInstance(id, name) {
            this.$hMsgBoxSafe.confirm({
                title: '删除',
                content: `您确定删除名为"${name}"的应用节点实例吗？`,
                onOk: async () => {
                    const res = await delManageInstance({
                        productId: this.productInfo.id,
                        id
                    });
                    if (res.success) {
                        this.$hMessage.success('删除成功');
                        this.initData();
                    }
                }
            });
        }
    }
};
</script>

<style lang="less" scoped>

.tab-box {
    position: relative;
    width: 100%;
    height: calc(100% - 15px);

    .table-box {
        height: calc(100% - 102px);
    }

    /deep/ .icon-t-b-passwordreset:hover {
        color: var(--icon-hover) !important;
    }
}
</style>

<!--
 * @Description: 核心数据上场
 * @Author: <PERSON><PERSON>
 * @Date: 2023-09-01 15:01:37
 * @LastEditTime: 2024-08-26 16:04:57
 * @LastEditors: yingzx38608 <EMAIL>
-->
<template>
    <div class="main">
        <!-- 标题 -->
        <header>
            <a-title title="核心数据上场">
                <slot>
                    <div class="title-box">
                        <div class="tab-count">总上场次数：{{ totalCount }}</div>
                        <h-icon
                            class="icon-setting"
                            name="setup"
                            size="18"
                            color="var(--font-color)"
                            @on-click="showSettingModel"></h-icon>
                        <h-select
                            v-show="productList.length > 1"
                            v-model="productInstNo"
                            placeholder="请选择"
                            :positionFixed="true"
                            :clearable="false"
                            @on-change="checkProduct">
                            <h-option
                                v-for="item in productList"
                                :key="item.id"
                                :value="item.productInstNo"
                                >{{ item.productName }}</h-option>
                        </h-select>
                    </div>
                </slot>
            </a-title>
        </header>
        <!-- 数据上场 -->
        <div class="tab-wrapper">
            <data-accord-monitor
                ref="data-accord-monitor"
                :productInstNo="productInstNo"
                type="marketStart"
                :dataSyncDiffNum="dataSyncDiffNum"
                :autoRefreshRate="autoRefreshRate"
                @set-count="setTotalCount"
            />
            <h-drawer
                v-model="visible"
                width="400"
                title="配置"
                class="drawer-content">
                <h-row>
                    <h-col span="12">
                        主备核心数据同步差量告警：
                    </h-col>
                    <h-input
                        v-model="dataSyncDiffNum"
                        type="int"
                        specialFilter
                        :specialLength="9"
                        :specialDecimal="0"
                        style="width: 130px;"
                        size="small"
                        @on-change="handleInputChange" /> 条
                </h-row>
                <h-row>
                    <h-col span="12">
                        数据自动刷新频率：
                    </h-col>
                    <h-select
                        v-model="autoRefreshRate"
                        size="small"
                        :clearable="false"
                        style="width: 130px;"
                        @on-change="handleSelectChange">
                        <h-option v-for="n in 6" :key="n" :value="n+4">
                            {{ n + 4 }}
                        </h-option>
                    </h-select> 秒
                </h-row>
            </h-drawer>
        </div>
    </div>
</template>
<script>
import _ from 'lodash';
import { mapState, mapActions } from 'vuex';
import aTitle from '@/components/common/title/aTitle';
import dataAccordMonitor from '@/components/ldpMonitor/dataAccordMonitor';

export default {
    components: { aTitle, dataAccordMonitor },
    data() {
        return {
            productInstNo: '',
            totalCount: 0,
            visible: false,
            dataSyncDiffNum: '',
            autoRefreshRate: 5
        };
    },
    mounted() {
        this.init();
    },
    beforeDestroy() {
        this.clearPolling();
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        })
    },
    watch: {
        productInstNo() {
            this.clearPolling();
            this.$nextTick(async () => {
                this.$refs?.['data-accord-monitor'] && this.$refs['data-accord-monitor'].init();
            });
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        async init(){
            await this.getProductList({ filter: 'excludeLdpApm' });
            const productInstNo = localStorage.getItem('productInstNo');
            this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
            this.dataSyncDiffNum = localStorage.getItem('ldpAccordDataSyncDiffNum') || '';
            this.autoRefreshRate = Number(localStorage.getItem('ldpAccordAutoRefreshRate')) || 5;
        },
        // 切换产品
        checkProduct(e) {
            if (this.productList.length) {
                this.productInfo = e ? _.find(this.productList, ['productInstNo', e]) : this.productList[0];
                this.productInstNo = this.productInfo.productInstNo;
                localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            }
        },
        // 清空定时器
        clearPolling() {
            this.$refs?.['data-accord-monitor'] && this.$refs['data-accord-monitor'].clearPolling();
        },
        //  设置上场总次数
        setTotalCount(val){
            this.totalCount = val;
        },
        // 控制弹窗
        showSettingModel(){
            this.visible = true;
        },
        // 告警数值设置
        handleInputChange(e){
            localStorage.setItem('ldpAccordDataSyncDiffNum', this.dataSyncDiffNum);
        },
        // 告警数值设置
        handleSelectChange(val) {
            localStorage.setItem('ldpAccordAutoRefreshRate', this.autoRefreshRate);
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/tab.less");
@import url("@/assets/css/drawer.less");

.title-box {
    display: flex;
    position: absolute;
    top: 5px;
    right: 10px;
    width: auto;
    height: 32px;
    line-height: 32px;

    .tab-count {
        color: var(--font-color);
        font-size: var(--font-size);
        margin-right: 10px;
    }

    .icon-setting {
        cursor: pointer;
        border: var(--border);
        border-radius: 4px;
        padding: 0 6px;
        margin-right: 10px;
        height: 32px;
        line-height: 31px;
    }

    .h-select {
        width: 200px;
        display: inline-block;
    }
}

.tab-wrapper {
    position: relative;
    width: 100%;
    margin-top: 6px;
    height: calc(100% - 51px);

    /deep/.h-tabs {
        height: auto;
        background: var(--main-color);
    }

    /deep/ .h-tabs-nav-container {
        height: 42px;
        width: calc(100% - 170px);
    }

    /deep/ .h-tabs-bar {
        margin-bottom: 10px;
        border-bottom: var(--border);
    }

    /deep/ .h-tabs-tab {
        padding: 10px 6px;
    }

    /deep/ .h-tabs-content-wrap {
        height: 0;
    }
}

.drawer-content {
    .h-row {
        height: 42px;
        line-height: 42px;

        .h-col {
            text-align: right;
        }
    }
}
</style>

<template>
  <div>
    <nav class="apm-header">
      <div class="apm-header-logo">
        <img alt="logo" :src="logo" />
      </div>
      <h-menu ref="nav" mode="horizontal" :theme="theme" @on-select="onChange">
        <h-menu-item v-for="item in menus" :key="item.name" :name="JSON.stringify(item)"
          :data-active="checkParentIsActive(item)">
          <Submenu v-if="item.child && item.child.length" :data-active="checkParentIsActive(item)"
            :name="JSON.stringify(item)">
            <template slot="title">
              <span class="main-title" :data-active="checkParentIsActive(item)">
                {{ item.name }}
                <svg t="1731651231834" class="icon" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="7822" width="200" height="200">
                  <path
                    d="M744.789333 363.989333H280.021333a17.066667 17.066667 0 0 0-13.653333 27.605334l233.216 262.4c6.4 7.210667 17.237333 7.594667 24.405333 1.194666l1.194667-1.194666 233.216-262.4a17.152 17.152 0 0 0-13.610667-27.605334z"
                    fill="#CACFD4" p-id="7823"></path>
                </svg>
              </span>
            </template>
            <h-menu-item v-for="subItem in item.child" :key="subItem.name" :name="JSON.stringify(subItem)">
              <span :data-active="activePath === subItem.uri">
                {{ subItem.name }}
              </span>
            </h-menu-item>
          </Submenu>
          <template v-else>
            <span class="single-title" :data-active="checkParentIsActive(item)">{{ item.name }}</span>
          </template>
        </h-menu-item>
      </h-menu>
      <div v-if="menuStack.length > 0" :data-active="checkStackIsActive" class="menu-stack" @mouseleave="resetMenuStackLeft" @mouseenter="hoverMenuStackIcon">
        <div class="menu-stack-icon">
          <svg t="1731899444740" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
            p-id="6171" width="200" height="200">
            <path
              d="M215.210667 448a64 64 0 1 1 0 128 64 64 0 0 1 0-128z m296.789333 0a64 64 0 1 1 0 128 64 64 0 0 1 0-128z m296.789333 0a64 64 0 1 1 0 128 64 64 0 0 1 0-128z"
              fill="#ffffff" p-id="6172"></path>
          </svg>
        </div>
        <div class="list-wraper" :style="{left: menuStackLeft + 'px' }">
          <div ref="stackMenuListRef" class="menu-stack-list">
          <div v-for="item in menuStack" :key="item.name" class="menu-stack-list-item">
            <div class="menu-stack-list-item-name" :data-active="checkParentIsActive(item)" @click="(e) => onChange(JSON.stringify(item), e)" @mouseenter="() => hoverSubMenuStack(item.name)">
              {{ item.name }}
              <svg v-if="item.child && item.child.length > 0" t="1731651231834" class="icon" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="7822" width="200" height="200">
                <path
                  d="M744.789333 363.989333H280.021333a17.066667 17.066667 0 0 0-13.653333 27.605334l233.216 262.4c6.4 7.210667 17.237333 7.594667 24.405333 1.194666l1.194667-1.194666 233.216-262.4a17.152 17.152 0 0 0-13.610667-27.605334z"
                  fill="#CACFD4" p-id="7823"></path>
              </svg>
              <div v-if="item.child && item.child.length > 0" class="wraper">
                <div :ref="item.name" class="menu-stack-list-item-child">
                  <div v-for="item in item.child" :key="item.name" class="menu-stack-list-item-child-item">
                    <div :data-active="checkParentIsActive(item)" class="menu-stack-list-item-child-item-name" @click="(e) => onChange(JSON.stringify(item), e)">
                      {{ item.name }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </nav>
    <div class="menu-history" :data-matchHistory="activeHistory === null">
      <h-tabs
        ref="historyMenuTabs"
        type="card"
        :value="activeHistory"
        showArrow
        :animated="false"
        @on-tab-remove="onRemoveHistory"
        @on-click="(name) => onChange(JSON.stringify(foundRoute(name)))"
      >
        <h-tab-pane
          v-for="tab in menuHistory"
          :key="tab.uri"
          :closable="true"
          :name="tab.uri"
          :label="tab.name"
        />
      </h-tabs>
    </div>
  </div>
</template>
<script>
import _ from 'lodash';
import { getMenus } from '@/api/systemApi';
import { generateMenu, getHashAndParam } from './util';
import { HISTORY_ROUTE_KEY, STATIC_MAX_MENU_WIDTH } from './constant';
import './header.less';

export default {
    data() {
        return {
            theme: 'dark',
            routers: [], // 菜单全集
            logo: `${this.IMG_HOME}static/APM-logo.png`,
            activePath: getHashAndParam().uri,
            headerHeight: '80px', // 菜单整体高度，包含菜单历史tab高度 + 路由高度
            menuStack: [], // 收缩的菜单，当屏幕过小时
            menus: [], // 显示的菜单，除了收缩的
            menuStackLeft: '0px', // 动态距离，保证显示完整
            rangeDistance: 0, // 保存计算缩略菜单的距离
            historyMenuHeight: '32px',
            menuHistory: [], // 查看历史路由
            activeHistory: null,
            cacuHistoryWidth: null
        };
    },
    mounted() {
        this.queryMenu();
        this.$nextTick(() => {
            this.injectUniqueStyle();
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.onResize);
    },
    watch: {
        $route(to) {
            if (this.activePath != to?.path) {
                this.activePath = to?.path;
                this.activeHistory = this.activePath;
            }
        },
        'menuHistory.length'() {
            const hiustoryCount = this.menuHistory.length;
            if (!hiustoryCount) return;
            this.menuHistory.forEach(menu => this.setItemHistoryStyle(menu));
            window.sessionStorage.setItem(HISTORY_ROUTE_KEY, JSON.stringify(this.menuHistory));
        }
    },
    methods: {
        /**
         * 加载本地历史
         */
        async loadLocalHistory() {
            const historyRoutesStr = window.sessionStorage.getItem(HISTORY_ROUTE_KEY);
            if (historyRoutesStr) {
                try {
                    const historyRoutes = JSON.parse(historyRoutesStr);
                    const menuHistory = historyRoutes.reduce((pre, item) => {
                        const match = this.routers.find(route => {
                            if (route.uri === item.uri) return true;
                            return route.child?.length ? !!route.child.find(subItem => subItem.uri === item.uri) : false;
                        });
                        if (!match) {
                            return pre;
                        }
                        const preRoutes = [...pre, item];
                        return preRoutes;
                    }, []);
                    if (menuHistory.length) {
                        this.menuHistory = menuHistory;
                        for await (const item of menuHistory) {
                            this.setItemHistoryStyle(item);
                        }
                    }
                } catch (error) {
                    console.error('解析历史路由失败', error);
                }
            }
        },
        /**
         * 设置历史菜单的宽度
         */
        setItemHistoryStyle(item) {
            this.$nextTick(() => {
                const tempEl = document.createElement('div');
                tempEl.innerHTML = `&nbsp;${item.name}&nbsp;`;
                tempEl.classList = 'historyMenuText';
                document.body.appendChild(tempEl);
                const width = tempEl.getBoundingClientRect()?.width;
                const tabs =  this.$refs['historyMenuTabs']?.$refs?.nav?.children || [];
                const tab = [...tabs].find(tab => tab.innerText === item.name);
                tab.style.width = `${width}px`;
                document.body.removeChild(tempEl);
            });
        },
        /**
         * 查询菜单
         */
        async queryMenu() {
            try {
                const res = await getMenus();
                if (res.data) {
                    this.routers = generateMenu(res.data);
                    this.loadLocalHistory();
                    this.$nextTick(() => {
                        window.addEventListener('resize', this.onResize);
                        this.callSetRouterLayout();
                        const defaultRoute = this.foundRoute(getHashAndParam().uri);
                        if (defaultRoute) {
                            this.activeHistory = defaultRoute.uri;
                            this.updateMenuHistory(defaultRoute.uri);
                        }
                    });
                }
            } catch (error) {
                console.error('菜单结构初始化异常:', error);
                this.$emit('setHidden');
                this.remoreUniqueStyle();
            }
        },
        /**
         * 监听窗口变化事件
         */
        onResize: _.throttle(function () {
            this.callSetRouterLayout();
            this.hoverMenuStackIcon();
        }, 10),
        /**
         * 动态设置菜单layout
         */
        callSetRouterLayout() {
            const routerCount = this.routers.length;
            if (!routerCount) return;
            const menuContentWidth = window.innerWidth - 192 - 48;
            if (routerCount * STATIC_MAX_MENU_WIDTH > menuContentWidth) {
                const diffCount =
          Math.abs(menuContentWidth - routerCount * STATIC_MAX_MENU_WIDTH) /
          STATIC_MAX_MENU_WIDTH;
                const rangeCount = Math.round(diffCount);
                const menuStackIndex =
          diffCount > rangeCount ? rangeCount + 1 : rangeCount;
                this.menuStack = this.routers.slice(-menuStackIndex);
                this.menus = this.routers.slice(0, routerCount - menuStackIndex);
            } else {
                this.menus = [...this.routers];
                this.menuStack = [];
            }
        },
        /**
         * 检查父级路由是否是选中状态
         */
        checkParentIsActive(parent) {
            const hastSelect =
        parent.child &&
        !!parent.child.find((item) => item.uri === this.activePath);
            if (!hastSelect) {
                const { uri } = parent;
                return this.activePath.includes(uri);
            }
            return true;
        },
        /**
         * 切换路由
         */
        onChange(itemString, event) {
            const item = JSON.parse(itemString);
            if (event) {
                event.stopPropagation();
                event.preventDefault();
            }
            if (item.uri) {
                this.$hCore.navigate(item.uri);
                this.activePath = item.uri;
                this.activeHistory = item.uri;
                this.updateMenuHistory(item.uri);
            }
        },
        /**
         * 注入自定义专属样式
         */
        injectUniqueStyle() {
            let styleTag = document.getElementById('unique_deploy_style');
            if (!styleTag) {
                styleTag = document.createElement('style');
                styleTag.id = 'unique_deploy_style';
                styleTag.innerHTML = `
              .h-drawer-left, .h-drawer-right {
                height: calc(100% - ${this.headerHeight}) !important;
                top: ${this.headerHeight};
              }
              .h-drawer-body {
                overflow-y: auto !important;
              }
              // .h-drawer-body::-webkit-scrollbar {
              //     width: 0px;
              //     display: none;
              // }
              .h-modal-wrap {
                top: ${this.headerHeight};
              }
              .apm-header ul.h-menu {
                min-width: ${this.routers.length * 117}px;
              }
            `;
                const defaultMian = document.querySelector('.default-main');
                if (defaultMian) {
                    const dataKeyName = defaultMian.attributes[0]?.name;
                    if (dataKeyName) {
                        styleTag.innerHTML += `
                .main[${dataKeyName}=""] {
                height: calc(100vh - ${this.headerHeight}) !important;
                position: relative;
              }
                `;
                    }
                }
                styleTag.setAttribute('type', 'text/css');
                document.head.appendChild(styleTag);
            }
        },
        /**
         * 移除自定义样式
         */
        remoreUniqueStyle() {
            const styleTag = document.getElementById('unique_deploy_style');
            if (styleTag) {
                document.head.removeChild(styleTag);
            }
        },
        /**
         * 鼠标hover菜单缩略icon
         */
        hoverMenuStackIcon() {
            const stackMenuListRef = this.$refs['stackMenuListRef'];
            if (stackMenuListRef) {
                const nav = this.$refs['nav']?.$el;
                if (nav) {
                    const width = stackMenuListRef.getBoundingClientRect().width;
                    const navWidth = nav.getBoundingClientRect()?.width;
                    const freeContentWidth = window.innerWidth - 192 - navWidth;
                    const rangeDistance = freeContentWidth - width;
                    this.rangeDistance = rangeDistance;
                    if (rangeDistance < 0) {
                        this.menuStackLeft = rangeDistance;
                    }
                }
            } else {
                this.menuStackLeft = 0;
            }
        },
        /**
         * 鼠标hover菜单缩略的一级菜单
         */
        hoverSubMenuStack(name) {
            const el = this.$refs[name]?.[0];
            if (el) {
                const width = el.getBoundingClientRect()?.width;
                this.menuStackLeft = this.rangeDistance - width;
            }
        },
        /**
         * 鼠标移出后，重置距离值
         */
        resetMenuStackLeft(){
            this.menuStackLeft = 0;
        },
        /**
         * 根据当前激活的路由，更新历史路由
         */
        updateMenuHistory(activePath) {
            const foundIndex = this.menuHistory.findIndex(item => item.uri === activePath);
            const curRoute = this.foundRoute(activePath);
            if (!curRoute) return;
            if (foundIndex === -1) {
                this.menuHistory.push(curRoute);
            }
        },
        /**
         * 根据uri获取路由对象
         */
        foundRoute(uri) {
            for (const item of this.routers) {
                const foundCurRoute = item.child?.length ? item.child.find(child => child.uri === uri) : undefined;
                if (foundCurRoute) {
                    return foundCurRoute;
                } else if (item.uri === uri) {
                    return item;
                }
            }
        },
        /**
         * 删除路由历史
         */
        onRemoveHistory(uri) {
            this.menuHistory = this.menuHistory.filter(item => item.uri !== uri);
            if (uri === this.activePath) {
                this.activeHistory = null;
            }
        }
    },
    computed: {
        /**
         * 当前激活菜单是否在更多菜单里
         */
        checkStackIsActive() {
            if (!this.menuStack.length) return false;
            return !!this.menuStack.find(item => {
                if (item.uri === this.activePath) return true;
                return item.child?.length ? item.child.find(child => child.uri === this.activePath) : false;
            });
        }
    }
};
</script>

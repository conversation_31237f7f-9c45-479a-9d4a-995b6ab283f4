<template>
    <div>
        <h-drawer
            ref="drawerBox"
            v-model="modalData.status"
            title="应用服务识别规则"
            width="700"
            :mask-closable="false"
            @on-close="handleClose">
            <h-spin v-if="loading" fix>
                <h-icon name="load-c" size="18" class="demo-spin-icon-load"></h-icon>
                <div>加载中...</div>
            </h-spin>

            <div class="service-rule-container">
                <h-row class="header-row">
                    <h-col :span="6"><span>应用身份</span></h-col>
                    <h-col :span="10"><span>应用节点类型</span></h-col>
                    <h-col :span="7"><span>服务名</span></h-col>
                    <h-col :span="1"></h-col>
                </h-row>
                <h-form ref="formDynamic" :model="formDynamic">
                    <div v-for="(rule, index) in formDynamic.rules" :key="`rule-${rule.uuid}`">
                        <h-row class="rule-row">
                            <h-col :span="6">
                                <h-form-item :prop="'rules.' + index + '.identity'" required>
                                    <h-select
                                        v-model="rule.identity"
                                        :clearable="false"
                                        positionFixed
                                        autoPlacement
                                        placeholder="请选择"
                                        class="identity-select"
                                        @on-change="handleChangeidentity($event, index)">
                                        <h-option
                                            v-for="item in identifyOptions"
                                            :key="item.value"
                                            :value="item.value">
                                            {{ item.label }}
                                        </h-option>
                                    </h-select>
                                </h-form-item>
                            </h-col>
                            <h-col :span="10">
                                <h-form-item :prop="'rules.' + index + '.instanceTypes'" required>
                                    <h-select
                                        v-model="rule.instanceTypes"
                                        multiple
                                        showTitle
                                        positionFixed
                                        autoPlacement
                                        placeholder="请选择"
                                        class="node-type-select">
                                        <h-option
                                            v-for="item in rule.typeOptions"
                                            :key="item.value"
                                            :value="item.value">
                                            {{ item.label }}
                                        </h-option>
                                    </h-select>
                                </h-form-item>
                            </h-col>
                            <h-col :span="7">
                                <h-form-item :prop="'rules.' + index + '.serviceName'" required>
                                    <h-input
                                        v-model.trim="rule.serviceName"
                                        placeholder="请输入服务名"
                                        :maxlength="30"
                                        class="service-input">
                                    </h-input>
                                </h-form-item>
                            </h-col>
                            <h-col :span="1">
                                <h-icon
                                    class="icon-setting"
                                    name="t-b-delete"
                                    color="#9296A1"
                                    size="16"
                                    @on-click="()=>removeRule(index, rule)"
                                ></h-icon>
                            </h-col>
                        </h-row>
                    </div>
                </h-form>
                 <a-button
                     type="dashed"
                     icon="add"
                     class="add-btn"
                     @click="addRule">
                     添加规则
                 </a-button>
            </div>

            <div slot="footer" class="drawer-footer">
                <a-button
                    type="primary"
                    @click="saveRules">
                    保存配置
                </a-button>
                <a-button
                    type="dark"
                    @click="handleClose">
                    关闭
                </a-button>
            </div>
        </h-drawer>
    </div>
</template>

<script>
import aButton from '@/components/common/button/aButton';
import { getProductInstances, saveServiceRules } from '@/api/productApi';
import { v4 as uuidv4 } from 'uuid';
export default {
    name: 'ShardingTableDetailDrawer',
    components: { aButton },
    props: {
        productId: {
            type: String,
            default: ''
        },
        modalInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            loading: false,
            modalData: this.modalInfo,
            formDynamic: {
                rules: []
            },
            identifyOptions: [],
            identityTypeMap: {},
            deleteRules: []
        };
    },
    mounted() {
        this.handleDrawerOpen();
    },
    computed: {
        instanceIdentityDict() {
            const instanceIdentityDict = this.$store?.state?.apmDirDesc?.instanceIdentityDict || {};
            instanceIdentityDict['unknown'] = '自定义';
            return instanceIdentityDict;
        },
        appTypeDictDesc() {
            return this.$store?.state?.apmDirDesc?.appTypeDictDesc || {};
        }
    },
    methods: {
        // 打开抽屉时的处理逻辑
        async handleDrawerOpen() {
            this.loading = true;

            const { instanceIdentitiesList, identityTypeMap } = await this.parseApplicationList();
            this.identifyOptions = (instanceIdentitiesList || []).map(item => {
                return {
                    value: item,
                    label: this.instanceIdentityDict[item] || item
                };
            });
            this.identityTypeMap = identityTypeMap;

            // 获取配置规则
            this.formDynamic.rules = this.modalData.rules.map(o => {
                const typeOptions = (identityTypeMap[o.identity || 'unknown'] || [])?.map(item => {
                    return {
                        value: item,
                        label: this.appTypeDictDesc[item] || item
                    };
                }) || [];
                return {
                    serviceCode: o.serviceCode,
                    uuid: uuidv4(),
                    serviceName: o.serviceName || '',
                    identity: o.identity || 'unknown',
                    instanceTypes: o.instanceTypes || [],
                    typeOptions: typeOptions || []
                };
            });

            this.loading = false;
        },
        handleClose() {
            this.modalData.status = false;
        },
        /**
         * 添加新的规则
         */
        addRule() {
            // 若存在空行，禁止添加
            const activeRules = this.formDynamic.rules || [];
            if (activeRules.some(rule => !rule.identity || !rule.serviceName || !rule.instanceTypes.length)) {
                this.$hMessage.warning('存在未完成的规则，无法添加新规则！');
                return;
            }

            this.formDynamic.rules.push({
                uuid: uuidv4(),
                serviceName: '',
                identity: '',
                instanceTypes: [],
                typeOptions: []
            });
        },
        /**
         * 移除规则
         */
        removeRule(index, rule) {
            // 判断该行serviceCode是否存在，判断删除的数据是历史数据还是当前操作新增后删除的
            const deleteItem = this.formDynamic.rules[index];
            this.formDynamic.rules.splice(index, 1);
            if (deleteItem.serviceCode) {
                this.deleteRules.push(rule);
            }
        },
        /**
         * 保存当前表单中的规则
         */
        saveRules() {
            // formDynamic校验
            this.$refs.formDynamic.validate(valid => {
                if (valid) {
                    // 如果验证通过，执行保存操作
                    this.saveConfigRules();
                }
            });
        },
        /**
         * 处理选择角色变化时的逻辑
         * 根据应用身份确定应用节点类型可选内容及服务名
         * 服务名生成规则：身份 + “服务”，
         * @param {number} index - 变化的角色在`formDynamic.rules`数组中的索引
         */
        handleChangeidentity(value, index) {
            const typeOptions = this.identityTypeMap[value]?.map(item => {
                return {
                    value: item,
                    label: this.appTypeDictDesc[item] || item
                };
            }) || [];
            this.formDynamic.rules[index].typeOptions = typeOptions;

            // 判断原节点类型的值是否存在于typeOptions中，不存在需要剔除
            this.formDynamic.rules[index].instanceTypes = this.formDynamic.rules[index].instanceTypes.filter(item =>
                typeOptions.some(option => option.value === item)
            );

            // 根据选择的身份生成服务名
            this.formDynamic.rules[index].serviceName = `${this.instanceIdentityDict[value] || value}服务`;
        },
        /**
         * 根据应用列表解析
         * 1. 应用身份列表
         * 2. 应用身份下应用类型列表（即关联关系）
         */
        async parseApplicationList() {
            const instances = await this.getProductInstances();

            const identityTypeMap = {}; // 初始化实例身份和类型映射对象
            const identitySet = new Set(); // 初始化实例身份集合，用于去重

            const insAllTypesSet = new Set(); // 当前产品应用类型集合，用于去重

            // 遍历实例数组
            for (const instance of instances) {
                insAllTypesSet.add(instance.instanceType);

                // 跳过没有实例身份的实例
                if (!instance.instanceIdentities) continue;

                // 遍历实例身份
                for (const identity of instance.instanceIdentities) {
                    identitySet.add(identity); // 将身份添加到集合中

                    // 初始化映射对象中的身份对应列表，或使用现有列表
                    identityTypeMap[identity] ||= [];
                    // 将实例类型添加到对应身份的列表中，并去重
                    if (instance.instanceType && !identityTypeMap[identity].includes(instance.instanceType)) {
                        identityTypeMap[identity].push(instance.instanceType);
                    }
                }
            }
            const instanceIdentitiesList = [...identitySet]; // 将集合转换为数组

            // 添加自定义身份及对应节点类型
            instanceIdentitiesList.push('unknown');
            identityTypeMap['unknown'] = [...insAllTypesSet];

            return { instanceIdentitiesList, identityTypeMap };
        },
        // 获取应用节点列表
        async getProductInstances() {
            let instances = [];
            const params = {
                productId: this.productId
            };
            const res = await getProductInstances(params);
            if (res.code === '200') {
                instances = res?.data?.instances;
            }
            return instances;
        },
        /**
         * 保存配置规则接口
         */
        async saveConfigRules() {
            const params = {
                productId: this.productId,
                activeServices: this.formDynamic.rules,
                deleteServices: this.deleteRules
            };
            const res = await saveServiceRules(params);
            if (res.code === '200') {
                this.$hMessage.success('保存成功');
                this.$emit('update');
                this.handleClose();
            }
        }
    }
};
</script>

<style lang="less" scoped>
@import url("@/assets/css/drawer.less");

.header-row {
    height: 40px;
    line-height: 40px;
    background-color: var(--primary-color);
    color: var(--font-color);
    font-size: 14px;
    margin-bottom: 8px;
    padding-left: 15px;
}

.rule-row {
    display: flex;
    align-items: center;
    padding: 5px 0 5px 15px;
    margin-bottom: 10px;
    background-color: var(--primary-color);

    /deep/ .h-form-item {
        margin-bottom: 0;
    }

    /* stylelint-disable-next-line selector-class-pattern */
    /deep/ .h-form-item-requiredIcon {
        display: none;
    }

    .h-col {
        padding-right: 15px;
    }

    .service-input,
    .identity-select,
    .node-type-select {
        width: 100%;
        margin-right: 10px;
    }

    .icon-setting {
        &:hover {
            cursor: pointer;
        }
    }
}

.add-btn {
    display: block;
    background: var(--wrapper-color);
    border: 1px dashed var(--border-color);
    color: var(--font-color);
}

.drawer-footer {
    height: 44px;
    padding: 6px 20px;
    border-top: var(--border);
    display: flex;
    gap: 8px;
}

.h-spin-fix {
    background: var(--wrapper-color);
}

.demo-spin-icon-load {
    animation: ani-demo-spin 1s linear infinite;
    display: inline-block;
}

@keyframes ani-demo-spin {
    from {
        transform: rotate(0deg);
    }

    50% {
        transform: rotate(180deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>

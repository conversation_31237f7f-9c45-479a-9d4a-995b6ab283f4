/* stylelint-disable selector-class-pattern */
// 自定义输入框，下拉框样式
/deep/ .h-form-item-label {
    color: var(--font-color) !important;
}

/deep/ .h-input {
    background-color: var(--input-bg-color);
    border: var(--border);
    border-radius: var(--border-radius);
    color: var(--font-color);
    text-overflow: ellipsis;
}

/deep/ .h-input-icon {
    cursor: pointer;

    &:hover {
        color: var(--link-color);
    }
}

/deep/ .h-input input {
    color: var(--font-color);
}

/deep/ .h-input-group-append {
    background-color: var(--input-bg-color);
    border: none;
}

/deep/ .h-input-group-append .h-select {
    margin: -3px -8px;
}

/deep/.h-form-item-error .h-input {
    border-color: var(--error-color) !important;
}

/deep/ .h-select > .h-select-left,
/deep/ .h-selectTree > .h-selectTree-selection {
    color: var(--font-color);
    background-color: var(--input-bg-color);
    border: var(--border);
}

/deep/ .h-select-content-input,
/deep/ .h-select-input {
    color: var(--font-color);
    line-height: 30px;
    height: 30px;
}

/deep/ .h-select {
    .h-checkbox-inner {
        border: var(--border);
        background-color: var(--font-color);
    }
}

/deep/ .h-selectTable {
    min-width: 150px;
    display: inline-block;
    margin-right: 5px;
}

/deep/ .h-select-selection .h-select-tag-default {
    background: #33394e !important;
    border: 1px solid #485565 !important;

    .h-tag-text {
        color: var(--font-color);
    }

    .h-icon-close {
        color: #9296a1;
    }
}

/deep/.h-form-item-error .h-select-selection {
    border: 1px solid var(--error-color);
}
// drawer
/deep/ .h-drawer-content {
    background: var(--main-color);
    color: var(--font-color);
}

/deep/ .h-drawer-header {
    border-bottom: var(--border);
}

/deep/ .h-drawer-header-inner {
    color: var(--font-color);
}

// checkbox 公共样式
/deep/ .h-checkbox.h-checkbox-disabled > .h-checkbox-inner {
    background-color: var(--border-color) !important;
    border-color: var(--border-color) !important;
}

/deep/ .h-checkbox.h-checkbox-disabled + span {
    color: var(--border-color);
}

/deep/ .h-checkbox + span {
    color: var(--font-color);
}

/deep/ .h-checkbox-inner {
    border: 1px solid var(--font-color);
    border-radius: 2px;
    background: var(--input-bg-color);
}

/deep/ .h-checkbox-inner::after {
    border: none;
}

/deep/.h-checkbox-checked > .h-checkbox-inner::after {
    border: 2px solid var(--font-color);
    border-top: 0;
    border-left: 0;
}

/deep/ .h-checkbox-checked > .h-checkbox-inner {
    border-color: var(--link-color) !important;
    background: var(--link-color) !important;
}


/deep/ .h-checkbox-group {
    display: flex;
    align-items: center;
    height: 30px;
    margin-top: 5px;
    justify-content: center;
    color: var(--font-color);

    & > .h-checkbox-group-item {
        padding: 0 8px;
    }
}

// radio
/deep/ .h-radio-inner {
    background-color: var(--wrapper-color);
}

/deep/ .h-radio-group-item {
    background: var(--primary-color);
    border-color: #485565;
    color: var(--font-color);
}

/deep/ .h-radio-group-button .h-radio-wrapper:first-child {
    border-left: 1px solid #485565;
}

/deep/ .h-radio-group-button .h-radio-wrapper-checked {
    position: relative;
    border-color: #298dff;
}

/deep/ .h-radio-group-button .h-radio-wrapper-checked:first-child {
    border-color: #298dff;
}


// switch样式
.h-switch-disabled {
    background-color: #23293d;
    border-color: #485565;
    opacity: 0.5;
}

.h-switch-disabled .slide {
    background: #91949e;
}

.h-switch-checked {
    background-color: #298dff;
    border-color: #485565;
}

<template>
  <div ref="table-box" class="table-box">
    <a-simple-table
      ref="table"
      showTitle
      :columns="columns"
      :tableData="tableData"
      :loading="tableLoading"
      :total="totalCount"
      hasPage
      @query="queryTable"
    >
    </a-simple-table>

    <detail-drawer
      v-if="drawerInfo.status"
      :productId="productId"
      :modalInfo="drawerInfo"
      @download="download"
      @resetLogin="callResetLogin"
    />
  </div>
</template>

<script>
// import aSimpleTable from '@/components/common/table/aSimpleTable';
import importStatusTableIcon from '@/components/secondAppearance/importStatusTableIcon.vue';
import {
    getExportHistory,
    downloadMdb,
    deleteExportHistory
} from '@/api/mdbExportApi';
import { getCurrentDatetime } from '@/utils/utils';
import { STATUS_COLOR_MAP, TASK_EXECUTE_STATUS } from './constant';
import { MDB_NO_LOGIN } from '@/config/errorCode';
import detailDrawer from './detailDrawer.vue';
import aSimpleTable from '@/components/common/table/aTable';

export default {
    components: {
        aSimpleTable,
        detailDrawer
    },
    props: {
        productId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            tableLoading: false,
            tableHeight: 200,
            totalCount: 0,
            deleteRemoteFile: true,
            deleteLocalFile: true,
            timer: null,
            columns: [
                {
                    title: '任务ID',
                    key: 'taskId',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: '导出时间',
                    key: 'time',
                    minWidth: 150,
                    ellipsis: true
                },
                {
                    title: '导出状态',
                    minWidth: 120,
                    render: (_, params) => {
                        const text =
              TASK_EXECUTE_STATUS.find(
                  (status) => status.value === params.row.status
              )?.label || '';
                        let sqlTableIconType;
                        switch (params.row.status) {
                            case 'partSuccess':
                                sqlTableIconType = 'warn';
                                break;
                            case 'finished':
                                sqlTableIconType = 'success';
                                break;
                            case 'error':
                                sqlTableIconType = 'error';
                                break;
                            case 'exporting':
                                sqlTableIconType = 'loading';
                                break;

                        }
                        return (
                            <div>
                                <importStatusTableIcon type={sqlTableIconType} />
                                {text}
                                {params.row.failInfo ? (
                                    <h-poptip
                                        autoPlacement
                                        transfer
                                        customTransferClassName="apm-poptip"
                                        content={params.row.failInfo}
                                        trigger="click"
                                    >
                                        <span>
                      ，<span class="click-text hover-underline">查看原因</span>
                                        </span>
                                    </h-poptip>
                                ) : (
                                    ''
                                )}
                            </div>
                        );
                    }
                },
                {
                    title: '导出内存表数量',
                    key: 'result',
                    minWidth: 200,
                    ellipsis: true,
                    render: (h, { row }) => {
                        const { successNum, totalCount } = row;
                        const spanText = `${successNum ?? '-'} / ${totalCount}`;
                        return h(
                            'div',
                            {
                                attrs: {
                                    class: 'h-table-cell-ellipsis',
                                    title: spanText
                                }
                            },
                            spanText
                        );
                    }
                },
                {
                    title: '操作',
                    key: 'action',
                    width: 160,
                    // fixed: 'right',
                    // render: (h, params) => {
                    //     const { taskId } = params.row;
                    //     return h('span', [
                    //         h(
                    //             'a',
                    //             {
                    //                 on: {
                    //                     click: () => {
                    //                         this.download({ taskId }, `${taskId}_${getCurrentDatetime()}.zip`);
                    //                     }
                    //                 }
                    //             },
                    //             '下载'
                    //         ),
                    //         h(
                    //             'a',
                    //             {
                    //                 style: {
                    //                     margin: '0 15px'
                    //                 },
                    //                 on: {
                    //                     click: () => {
                    //                         this.viewDetail(params.row.taskId);
                    //                     }
                    //                 }
                    //             },
                    //             '详情'
                    //         ),
                    //         h(
                    //             'a',
                    //             {
                    //                 on: {
                    //                     click: () => {
                    //                         this.$hMsgBoxSafe.confirm({
                    //                             title: `删除`,
                    //                             render: (h) =>
                    //                                 h('div', [
                    //                                     h(
                    //                                         'div',
                    //                                         `删除导出任务的同时，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。`
                    //                                     ),
                    //                                     h(
                    //                                         'h-checkbox',
                    //                                         {
                    //                                             props: {
                    //                                                 value: this.deleteRemoteFile
                    //                                             },
                    //                                             on: {
                    //                                                 'on-change': (v) => {
                    //                                                     this.deleteRemoteFile = v;
                    //                                                 }
                    //                                             }
                    //                                         },
                    //                                         '删除远程服务器数据'
                    //                                     ),
                    //                                     h(
                    //                                         'h-checkbox',
                    //                                         {
                    //                                             props: {
                    //                                                 value: this.deleteLocalFile
                    //                                             },
                    //                                             on: {
                    //                                                 'on-change': (v) => {
                    //                                                     this.deleteLocalFile = v;
                    //                                                 }
                    //                                             }
                    //                                         },
                    //                                         '删除APM服务器数据'
                    //                                     )
                    //                                 ]),
                    //                             onOk: async () => {
                    //                                 this.handleDelete(params.row.taskId);
                    //                             }
                    //                         });
                    //                     }
                    //                 }
                    //             },
                    //             '删除'
                    //         )
                    //     ]);
                    // },
                    render: (_, { row }) => {
                        const canDownload = [
                            STATUS_COLOR_MAP.finished.value
                        ].includes(row.status);
                        const taskId = row.taskId;
                        return (
                            <span>
                                {
                                    row.loading && <importStatusTableIcon type="loading" />
                                }
                                <a onClick={async () => {
                                    if (!canDownload || row.loading) return;
                                    this.tableData = this.tableData.map(item => ({
                                        ...item,
                                        loading: taskId ===  item.taskId
                                    }));
                                    try {
                                        await this.download({ taskId }, `${taskId}_${getCurrentDatetime()}.zip`);
                                    } finally {
                                        row.loading = false;
                                        this.tableData = this.tableData.map(item => ({
                                            ...item,
                                            loading: false
                                        }));
                                    }
                                }} class={canDownload && !row.loading ? 'pointer' : 'not-allowed'} style={{ cursor: canDownload && !row.loading ? 'pointer' : 'not-allowed' }}>下载</a>
                                <a style={{ margin: '0 15px' }} onClick={() => this.viewDetail(taskId)}>详情</a>
                                <a onClick={() => this.onDelete(taskId)}>删除</a>
                            </span>
                        );
                    }
                }
            ],
            tableData: [],
            drawerInfo: {
                status: false
            }
        };
    },
    mounted() {
        window.addEventListener('resize', this.fetTableHeight);
        this.fetTableHeight();
        this.handleSetInterval();
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.fetTableHeight);
        if (this.timer) {
            clearInterval(this.timer);
            this.timer = null;
        }
    },
    methods: {
    // 初始化数据方法
        async initData(page = { pageSize: 10, page: 1 }, ignoreLoading) {
            !ignoreLoading && (this.tableLoading = true);
            try {
                // 调用接口获取表格数据
                const params = {
                    productId: this.productId,
                    ...page
                };
                const res = await getExportHistory(params);
                if (res?.code === MDB_NO_LOGIN) {
                    this.callResetLogin();
                    return;
                }
                if (res.code === '200') {
                    this.tableData = res.data?.list || [];
                    this.totalCount = res.data?.totalCount || 10;
                    this.$nextTick(() => {
                        this.fetTableHeight();
                    });
                } else {
                    this.tableData = [];
                    this.totalCount = 10;
                    this.$hMessage.error(res.message);
                }
            } catch (err) {
                // 捕获错误并打印
                console.error(err);
                this.totalCount = 10;
                this.tableData = [];
            }
            this.tableLoading = false;
        },
        handleSetInterval() {
            // 每隔 5s 调用后端接口最近导出状态,并更新 UI 界面
            this.timer = setInterval(async () => {
                await this.queryTable(true);
            }, 5000);
        },
        /**
     * token过期，登出
     */
        callResetLogin() {
            if (this.drawerInfo.status) {
                this.drawerInfo.status = false;
            }
            this.$emit('resetLogin');
        },
        queryTable(ignoreLoading) {
            const pageInfo = this.$refs['table']?.getPageData();
            this.initData(pageInfo || {}, ignoreLoading);
        },
        onDelete(taskId) {
            this.$hMsgBoxSafe.confirm({
                title: `确定删除任务和导出数据？`,
                render: (h) =>
                    h('div', [
                        h(
                            'div',
                            `删除导出任务，可同时删除该任务导出在服务器的内存表数据。请勾选需要删除的内容。`
                        ),
                        h(
                            'h-checkbox',
                            {
                                props: {
                                    value: this.deleteRemoteFile
                                },
                                on: {
                                    'on-change': (v) => {
                                        this.deleteRemoteFile = v;
                                    }
                                }
                            },
                            '删除远程服务器数据'
                        ),
                        h(
                            'h-checkbox',
                            {
                                props: {
                                    value: this.deleteLocalFile
                                },
                                on: {
                                    'on-change': (v) => {
                                        this.deleteLocalFile = v;
                                    }
                                }
                            },
                            '删除APM服务器数据'
                        )
                    ]),
                onOk: async () => {
                    this.handleDelete(taskId);
                }
            });
        },
        /**
     * 删除任务
     */
        async handleDelete(taskId) {
            try {
                this.tableLoading = true;
                const res = await deleteExportHistory({
                    taskId,
                    deleteRemoteFile: this.deleteRemoteFile,
                    deleteLocalFile: this.deleteLocalFile
                });
                if (res?.code === MDB_NO_LOGIN) {
                    this.callResetLogin();
                    return;
                }
                if (res?.code === '200') {
                    this.$hMessage.success('已删除');
                    this.$refs['table']?.resetPage();
                    this.$refs['table']?.resetPageSize();
                    this.queryTable();
                } else {
                    this.$hMessage.error(res?.message || '删除失败');
                }
            } catch (error) {
                console.log('删除历史任务失败,', error);
            } finally {
                this.tableLoading = false;
            }
        },
        // 设置表格高度方法
        fetTableHeight() {
            this.tableHeight = this.$refs['table-box']?.offsetHeight - 60;
            const tableEl = this.$refs['table-box'];
            if (tableEl) {
                const target = tableEl.querySelector('.h-table-wrapper');
                if (target) {
                    const diff = window.LOCAL_CONFIG?.HEADER_VISIBLE ? 0 : 80;
                    target.style.height = `${window.innerHeight - 250 + diff}px`;
                    target.style.overflowY = 'auto';
                }
            }
        },
        /**
     * 查看详情
     */
        viewDetail(taskId) {
            this.drawerInfo.status = true;
            this.drawerInfo.taskId = taskId;
        },
        /**
     * 下载单张表
     */
        async download(param, name) {
            try {
                const res = await downloadMdb({ ...param, productId: this.productId });
                if (res?.code === MDB_NO_LOGIN) {
                    this.callResetLogin();
                    return;
                }
                // blob类型 text/xml
                if (res?.type !== 'application/json') {
                    const url = URL.createObjectURL(res); // 生成临时下载链接
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = name;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    window.URL.revokeObjectURL(url);
                    this.$hMessage.success('下载成功!');
                } else if (res?.type === 'application/json') {
                    const text = await res.text();
                    const json = JSON.parse(text);
                    this.$hMsgBoxSafe.error({
                        title: '下载失败',
                        content: json?.message
                    });
                }
            } catch (error) {
                console.log('下载单张表失败,', error);
            }
        }
    }
};
</script>
<style lang="less">
@import url("@/assets/css/poptip-1.less");

.apm-poptip .h-poptip-inner {
    .h-poptip-body-content {
        max-width: 400px;
        max-height: 200px;
        white-space: normal;
        word-break: break-all;
    }

    .h-poptip-body-content-inner {
        color: var(--font-color);
    }
}

.hover-underline:hover {
    text-decoration: underline;
    text-decoration-color: #2d8de5;
}
</style>

<style lang="less" scoped>
/* 组件样式 */
.table-box {
    // height: calc(100% - 60px);
    padding: 0 10px;

    .obs-table {
        background-color: unset;
    }

    /deep/ .a-table {
        height: 100%;

        .h-table-wrapper {
            height: calc(100% - 100px);
        }
    }

    /deep/ .h-table td {
        background-color: unset;
    }

    /deep/ .h-page {
        // margin-top: 20px;
        // float: right;
        padding-top: 10px;
    }

    .not-allowed {
        color: #969797;

        &:hover {
            cursor: not-allowed;
            color: #969797;
        }
    }
}

.click-text {
    color: var(--link-color);

    &:hover {
        cursor: pointer;
    }
}
</style>

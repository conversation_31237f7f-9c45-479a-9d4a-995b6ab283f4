<template>
    <div class="grid-box">
        <div ref="wrapper" class="wrapper-box">
            <grid-layout
                :layout.sync="layout"
                :col-num="colNum"
                :row-height="rowHeight"
                :is-draggable="false"
                :is-resizable="false"
                :is-mirrored="false"
                :vertical-compact="true"
                :margin="[4, 10]"
                :use-css-transforms="true"
                :style="gridItemStyle">
                <grid-item
                    v-for="(item, idx) in layout"
                    :key="item.i"
                    class="service-box"
                    :x="item.x"
                    :y="item.y"
                    :w="item.w"
                    :h="item.h"
                    :i="item.i">
                    <business-box
                        :ref="type"
                        :panel="panels[idx]"
                        :type="type"
                        :placement="item.x < colNum / 2 ? 'right' : 'left'"
                        :mode="type"
                        @drawser-open="handleDrawerOpen" />
                </grid-item>
            </grid-layout>
            <no-data v-if="!panels.length" />
            <a-loading v-if="loading" style="width: 100%; height: 100%;"></a-loading>
        </div>
        <h-drawer
            ref='drawer-box'
            v-model="drawerVisible"
            width="40"
            :title="drawerTitle"
            class-name="info-drawer-box"
            @on-close="handleDrawerClose"
        >
            <div class="info-drawer">
                <h-checkbox v-model="tableInProcess" @on-change="setTableData">数据差量不为0</h-checkbox>
                <h-input v-model.trim="tableName"  placeholder="请输入表名"  icon='search' @on-blur="setTableData" @on-click="setTableData"/>
            </div>
            <obs-table
                showTitle
                :tableData="tableData"
                :columns="columns"
                :maxHeight="tableHeight"
                :loading="tableLoading" />
        </h-drawer>
    </div>
</template>
<script>
import _ from 'lodash';
import { GridLayout, GridItem } from 'vue-grid-layout';
import aLoading from '@/components/common/loading/aLoading';
import obsTable from '@/components/common/obsTable/obsTable';
import businessBox from '@/components/ldpProduct/businessBox/clusterBusinessBox';
import noData from '@/components/common/noData/noData';
import { isDivisible, getObjByArray } from '@/utils/utils';
import { getClusterRoles, getLoadDataDetail, getMonitorHeartbeats, getInstanceObservablesImportData } from '@/api/httpApi';
import { getProductObservation } from '@/api/topoApi';
const eunms = {
    0: '未加载',
    1: '加载中',
    2: '加载完成',
    3: '加载失败'
};
const columns = [
    {
        title: '表名',
        key: 'tableName',
        ellipsis: true
    },
    {
        title: '内存记录数',
        key: 'mdbRecordNumber',
        ellipsis: true
    },
    {
        title: '持久化记录数',
        key: 'databaseRecordNumber',
        ellipsis: true
    },
    {
        title: '数据差量',
        key: 'dataDifference',
        ellipsis: true
    }
];

const extraColumns = [
    {
        title: '加载状态',
        key: 'loadStatus',
        ellipsis: true,
        render: (h, params) => {
            return h('span', [eunms[String(params?.row?.loadStatus)] || '-']);
        }
    }
];

export default {
    props: {
        productInstNo: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        },
        dataSyncDiffNum: {
            type: String,
            default: ''
        },
        autoRefreshRate: {
            type: Number,
            default: 5
        }
    },
    components: { GridLayout, GridItem, businessBox, aLoading, noData, obsTable },
    data() {
        return {
            layout: [],
            panels: [],
            rowHeight: 0,
            colNum: 12,
            layoutStu: true,
            timer: null,
            loading: false,
            groupMode: 'sharding',
            hiddenArbInstance: true,
            drawerVisible: false,
            // 以下为 h-drawer 内容
            tableLoading: false,
            tableInProcess: false,
            tableHeight: 0,
            tableName: '',
            tableData: [],
            allTableData: [],
            columns: [],
            drawerTitle: '表详情'
        };
    },
    computed: {
        gridItemStyle() {
            return { minWidth: this.panels.length * 170 + 'px' };
        }
    },
    watch: {
        autoRefreshRate(newVal) {
            if (!isNaN(newVal) && newVal > 0) {
                this.setPolling(newVal);
            }
        }
    },
    mounted() {
        window.addEventListener('resize', this.resize);
    },
    beforeDestroy() {
        this.timer && clearInterval(this.timer);
        window.removeEventListener('resize', this.resize);
    },
    methods: {
        // 初始化
        async init() {
            this.clearPolling();
            this.handleDrawerClose();
            this.loading = true;
            this.setPolling(this.autoRefreshRate || 5);
            const panels = await this.getObservablesDashboards();
            if (!panels) return;
            this.generateLayout(panels);

            await this.generateStatus();
            this.loading = false;
            this.resetLayout();
        },
        // 重置layout
        resetLayout() {
            this.layoutStu = false;
            this.$nextTick(() => {
                this.rowHeight = parseInt(this.$refs.wrapper?.clientHeight / 10 - 5 * 2, 10);
                this.layoutStu = true;
            });
        },
        // 获取模版接口
        getObservablesDashboards() {
            // 应用节点观测仪表盘
            const param = {
                productId: this.productInstNo,
                observationMode: 'service',
                instanceIdentity: 'bizproc'
            };
            return new Promise((resolve, reject) => {
                getProductObservation(param).then(res => {
                    let panels = [];
                    if (res.success) {
                        panels = this.generatePanels(res?.data?.services || []);
                        resolve(panels);
                    } else {
                        resolve(false);
                    }
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 生成面板
        generatePanels(list) {
            const panels = [];
            list.forEach(item => {
                const data = {
                    content: [],
                    id: item.serviceCode,
                    name: item.serviceName
                };
                if (Array.isArray(item?.appClusters) && item?.appClusters?.length) {
                    item.appClusters.forEach(ele => {
                        const cluster = {
                            id: ele.id,
                            label: ele.clusterName,
                            nodes: [],
                            target: {
                                resourceId: ele.id,
                                resourceNameLeft: `分片${ele.shardingNo || '-'}`,
                                resourceNameRight: ''
                            }
                        };
                        if (Array.isArray(ele?.instances) && ele?.instances?.length) {
                            ele.instances.forEach(instance => {
                                const node = {
                                    id: instance.id,
                                    label: instance.instanceName,
                                    target: {
                                        resourceId: instance.id,
                                        resourceName: instance?.instanceName,
                                        resourceType: instance?.instanceType,
                                        instanceInfo: instance,
                                        runningInfo: {
                                            ...instance
                                        }
                                    }
                                };
                                cluster.nodes.push(node);
                            });
                        }
                        data.content.push(cluster);
                    });
                }
                panels.push(data);
            });
            return panels;
        },
        // 生成模型
        generateLayout(panels) {
            this.colNum = isDivisible(panels.length);
            const w = this.colNum / panels.length;
            this.layout = [];
            this.panels = [];
            panels.forEach((item, index) => {
                this.layout.push({
                    x: index * w,
                    y: 0,
                    w,
                    h: 10,
                    i: item.id.toString()
                });
                this.panels.push({
                    ...item
                });
            });
        },
        // 设置上场总次数
        setTotalCount(val){
            this.$emit('set-count', val);
        },
        // ----------------------------------------页面接口-------------------------------------------------------------
        // 应用实例数据盘前数据
        getImportDataInfo() {
            const param = this.dataSyncDiffNum ? {
                productId: this.productInstNo,
                dataSyncDiffNum: Number(this.dataSyncDiffNum)
            } : {
                productId: this.productInstNo
            };
            return new Promise((resolve, reject) => {
                getInstanceObservablesImportData(param).then(res => {
                    let data = {};
                    if (res.success) {
                        data = res?.data || {};
                    }
                    resolve(data);
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 获取集群角色
        getClusterRoles() {
            const param = {
                productId: this.productInstNo
            };
            return new Promise((resolve, reject) => {
                getClusterRoles(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'instanceId');
                    }
                    resolve(data);
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 获取心跳数据
        getMonitorHeartbeats() {
            const param = {
                productId: this.productInstNo,
                type: 'instance'
            };
            return new Promise((resolve, reject) => {
                getMonitorHeartbeats(param).then(res => {
                    let data = {};
                    if (res.success && Array.isArray(res.data)) {
                        data = getObjByArray(res.data, 'id');
                    }
                    resolve(data);
                }).catch(error => {
                    this.clearPolling();
                    reject(error);
                    console.error(error);
                });
            });
        },
        // 往模型中加入节点告警状态、业务状态、进程状态、分组信息、加载信息
        async generateStatus() {
            const appStus = await this.getMonitorHeartbeats();
            const dataInfo = await this.getImportDataInfo();
            const getClusterRoles = await this.getClusterRoles();
            const panels = _.cloneDeep(this.panels || []);

            // 设置上场总次数
            this.setTotalCount(dataInfo?.importTotalCount || 0);
            for (const panel of Object.values(panels)) {
                for (const element of Object.values(panel.content || [])) {
                    const cluster = _.filter(dataInfo?.appClusters || [], ['clusterId', element?.target?.resourceId || ''])?.[0];
                    const instances = getObjByArray(cluster?.instances || [], 'instanceId');
                    element.target.resourceNameRight = '上场次数:' + (cluster?.importCount || 0);
                    for (const ele of Object.values(element.nodes || [])) {
                        const instance = instances[ele?.target?.resourceId] || {};
                        const clusterRole = getClusterRoles[ele?.target?.resourceId]?.clusterRole || '';
                        ele.target.runningInfo = {
                            ...(ele.target.runningInfo || {}),
                            ...instance, // 弹窗数据
                            healthStatus: instance?.loadInfo?.healthStatus || '',  // 节点告警状态
                            appStatus: appStus?.[ele?.target.resourceId]?.status || '', // 节点进程状态
                            loadStatus: appStus?.[ele?.target.resourceId]?.loadStatus || '', // 节点数据上场状态
                            clusterRole  // 集群角色 主 备
                        };
                    }
                }
            }
            this.panels = [...panels];
        },
        // 盘前表格详情
        async getLoadDataDetail(id){
            const param = {
                productId: this.productInstNo,
                instanceId: id
            };
            try {
                const res = await getLoadDataDetail(param);
                if (id !== param.instanceId || this.productInstNo !== param.productId) return;
                if (res.code === '200'){
                    this.allTableData = [...res?.data || []];
                } else {
                    this.handleDrawerClose();
                }
            } catch (err) {
                this.handleDrawerClose();
                console.error(err);
            }
        },
        // ----------------------------------------DrawerBox-------------------------------------------------------------
        // 查看
        async handleDrawerOpen(val){
            this.fetTableHeight();
            this.drawerTitle = val?.title || '表详情';
            this.drawerVisible = true;
            this.tableLoading = true;
            if (this.drawerTitle === '表加载详情') {
                this.columns = [...columns, ...extraColumns];
            } else {
                this.columns = [...columns];
            }
            await this.getLoadDataDetail(val?.instanceId);
            this.setTableData();
            this.tableLoading = false;
        },
        // 关闭
        handleDrawerClose(){
            this.drawerVisible = false;
            this.tableInProcess = false;
            this.tableName = '';
            this.columns = [];
            this.allTableData = [];
            this.tableData = [];
            this.tableHeight = 0;
        },
        // 设置表格数据
        setTableData(){
            const searchTable = this.tableName ? _.filter([...this.allTableData], o => { return o?.tableName?.includes(this.tableName); }) : [...this.allTableData];
            const processData = this.tableInProcess ? _.filter([...searchTable], o => { return String(o?.dataDifference) !== '0'; }) : [...searchTable];
            this.tableData = [...processData];
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['drawer-box']?.$el?.offsetTop - (window.LOCAL_CONFIG.HEADER_VISIBLE === true ? 240 : 150);
        },
        // ----------------------------------------定时器-------------------------------------------------------------
        // 定时器
        setPolling(timerInterval) {
            this.clearPolling();
            this.timer = setInterval(() => {
                this.generateStatus();
            }, timerInterval * 1000);
        },
        // 清楚定时器
        clearPolling() {
            this.timer && clearInterval(this.timer);
        },
        // resize
        resize(){
            this.resetLayout();
            this.fetTableHeight();
        }
    }
};
</script>
<style lang="less" scoped>

@import url("@/assets/css/drawer.less");

.grid-box {
    width: 100%;
    height: 100%;

    .wrapper-box {
        width: 100%;
        height: 100%;
        position: relative;
        overflow-x: auto;
        overflow-y: hidden;
    }

    .service-box {
        padding-right: 3px;
        border-right: 1px solid #9797970e;
    }

    .app-grid-title {
        padding-left: 15px;
        color: #fff;
        font-size: 14px;
    }

    .app-grid-title::before {
        display: inline-block;
        position: relative;
        left: -10px;
        top: 2px;
        content: "";
        width: 4px;
        height: 15px;
        background: var(--link-color);
    }
}

/deep/ .obs-table {
    height: auto;

    .table-box {
        margin: 0;
    }
}
</style>

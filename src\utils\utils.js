/*
 * @Description: 通用方法封装
 * @Author: <PERSON><PERSON>
 * @Date: 2022-08-01 16:23:30
 * @LastEditTime: 2024-04-12 09:10:40
 * @LastEditors: yingzx38608 <EMAIL>
 */

/**
 * 导出json
 * @param data json数据
 * @param fileName 文件名
 */
import { exchangeList } from '@/config/exchangeConfig';
export const exportFile = (data, fileName, type) => {
    const parseData = type === 'json' ? JSON.stringify(data) : data;
    const uri =
        'data:text/csv;charset=utf-8,\ufeff' + encodeURIComponent(parseData);
    // 通过创建a标签实现
    const link = document.createElement('a');
    link.href = uri;
    // 对下载的文件命名
    link.download = `${fileName}.${type}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

/**
 * 导出csv
 * @param columns 列信息
 * @param label 文件名
 * @param tableRecords json数据
 */
export const exportCsv = (label, columns, tableRecords) => {
    // 处理数据的辅助函数
    const formatCsvValue = (value, column) => {
        // 处理特殊字符和格式
        // if (value === null || value === undefined) {
        //     return '';
        // }
        let formattedValue = String(value);
        // 检查列配置中是否指定了保持前导零
        const preserveZeros = column?.preserveZeros || // 通过列配置指定
                        /^0+\d+$/.test(formattedValue);

        // if (preserveZeros) {
        // 方法1：使用Excel文本标记
        // formattedValue = `="${formattedValue}"`;

        // 或者方法2：使用单引号（某些Excel版本可能更适用）
        // formattedValue = `'${formattedValue}`;
        // }

        // 处理其他特殊字符, 如果包含逗号、换行或引号，需要用引号包裹
        if (!preserveZeros && (formattedValue.includes(',') || formattedValue.includes('\n') || formattedValue.includes('"'))) {
            formattedValue = `"${formattedValue.replace(/"/g, '""')}"`;
        }

        return formattedValue;
    };
    // 准备表头和数据
    const headers = columns.map(col => col.title);
    const rows = tableRecords.map(record =>
        columns.map(col => formatCsvValue(record[col.key], col))
    );
    // 组合CSV内容
    const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    // 创建Blob对象并下载
    const blob = new Blob(['\ufeff' + csvContent], {
        type: 'text/csv;charset=utf-8'
    });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${label}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
};

/**
 * 不足位数补0
 * @number num 数值
 * @number n 位数
 * @return string
 */
export const prefixZero = (num, n) => {
    return (Array(n).join(0) + num).slice(-n);
};

/**
 * 格式化日期格式
 * @object date
 */
export const formatDate = (date) => {
    const y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    let d = date.getDate();
    d = d < 10 ? '0' + d : d;
    // var h = date.getHours();
    // h = h < 10 ? "0" + h : h;
    // var mi = date.getMinutes();
    // mi = mi < 10 ? "0" + mi : mi;
    // var s = date.getSeconds();
    // s = s < 10 ? "0" + s : s;
    return `${y}-${m}-${d}`;
};

// 时间戳转换
export const  formatTimeAgo = (timestamp) => {
    if (!timestamp) return '0';
    const currentTime = Math.floor(Date.now() / 1000);
    const timeDifference = Math.max(currentTime - timestamp, 0);
    const getPlural = (value, unit) => value <= 1 ? `${value} ${unit} ago` : `${value} ${unit}s ago`;

    if (timeDifference < 60) {
        return getPlural(timeDifference, 'second');
    } else if (timeDifference < 3600) {
        const minutes = Math.floor(timeDifference / 60);
        return getPlural(minutes, 'minute');
    } else if (timeDifference < 86400) {
        const hours = Math.floor(timeDifference / 3600);
        return getPlural(hours, 'hour');
    } else {
        const days = Math.floor(timeDifference / 86400);
        return getPlural(days, 'day');
    }
};

// 毫秒时间戳转换为yyyy-MM-dd hh:mm:ss格式
export const formatTimeDate = (value, split = '-') => {
    if (isNaN(value)) return '';
    const date = new Date(parseInt(value, 10));
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
};

// 时间段装换
export const getCurrentDatetime = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return String(String(String(String(String(year) + month) + day) + hours) + minutes) + seconds;
};
export const getTodayDate = () => {
    const curDate = new Date();
    return curDate;
};
export const getYesterdayDate = () => {
    const curDate = new Date();
    const preDate = new Date(curDate.getTime() - 24 * 60 * 60 * 1000); // 前一天
    return preDate;
};
export const getWeekDate = () => {
    const date = new Date();
    const week = date.getDay(); // 0-6，0 表示星期日
    const startDate = new Date(date.getTime() - (week - 1) * 24 * 60 * 60 * 1000);
    const endDate = new Date(date.getTime() + (7 - week) * 24 * 60 * 60 * 1000);
    return [startDate, endDate];
};
export const getMonthDate = () => {
    const date = new Date();
    const startDate = new Date(date.setDate(1)); // 本月第一天
    const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0);// 本月的最后一天
    return [startDate, endDate];
};

/**
 * 获取字符串字节长度
 * @string str
 */
export const getLength = (str) => {
    let length = 0;
    const reg = /[\u4e00-\u9fa5]/;
    for (let i = 0; i < str.length; i++) {
        if (reg.test(str.charAt(i))) {
            length += 2;
        } else {
            length++;
        }
    }
    return length;
};

/**
 * 获取字符串字节大小
 * @string str
 */
export const calculateByteSize = (str, digits = 2) => {
    const encoder = new TextEncoder();
    const encodedStr = encoder.encode(str);
    const size = encodedStr.length;
    if (size < 1024) {
        return size + 'B';
    } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(digits) + 'KB';
    } else {
        return (size / (1024 * 1024)).toFixed(digits) + 'MB';
    }
};

// 日期格式化(年月日时分秒)
export const formatDates = (date) => {
    const y = date.getFullYear();
    let m = date.getMonth() + 1;
    m = m < 10 ? '0' + m : m;
    let d = date.getDate();
    d = d < 10 ? '0' + d : d;
    let h = date.getHours();
    h = h < 10 ? '0' + h : h;
    let mi = date.getMinutes();
    mi = mi < 10 ? '0' + mi : mi;
    let s = date.getSeconds();
    s = s < 10 ? '0' + s : s;
    return `${y}-${m}-${d} ${h}:${mi}:${s}`;
};

// 判断是否是json字符串
export const isJSON = (str) => {
    if (typeof str == 'string') {
        try {
            const obj = JSON.parse(str);
            if (typeof obj == 'object' && obj) {
                return obj;
            } else {
                return false;
            }

        } catch (e) {
            console.log('error：' + str + '!!!' + e);
            return false;
        }
    }
};

// 获取字节长度
export const byteLength = (str) => {
    let len = 0;
    for (let i = 0; i < str.length; i++) {
        const a = str.charAt(i);
        // 使用的正则表达式
        if (a.match(/[^\x00-\xff]/ig) !== null) {
            len += 2;
        } else {
            len += 1;
        }
    }
    return len;
};

// 通过市场key值获取市场名
export const getExchangeName = (name) => {
    for (const item of exchangeList) {
        if (item.label === name) return item.value;
    }
};

// 舍去数字小数点后多余的0
export const cutZero = (old) => {
    // 拷贝一份 返回去掉零的新串
    let newstr = old;
    // 循环变量 小数部分长度
    const leng = old.length - old.indexOf('.') - 1;
    // 判断是否有效数
    if (old.indexOf('.') > -1) {
        // 循环小数部分
        for (let i = leng; i > 0; i--) {
            // 如果newstr末尾有0
            if (newstr.lastIndexOf('0') > -1 && newstr.substr(newstr.length - 1, 1) == 0) {
                const k = newstr.lastIndexOf('0');
                // 如果小数点后只有一个0 去掉小数点
                if (newstr.charAt(k - 1) == '.') {
                    return newstr.substring(0, k - 1);
                } else {
                    // 否则 去掉一个0
                    newstr = newstr.substring(0, k);
                }
            } else {
                // 如果末尾没有0
                return newstr;
            }
        }
    }
    return old;
};

// 分秒累加器
export const timeAccumulator = (secondsToAdd) => {
    // 获取当前时间
    const now = new Date();

    const list = [];

    // 循环累加秒数
    for (let i = 1; i <= secondsToAdd; i++) {
        // 增加一秒钟
        now.setSeconds(now.getSeconds() + 1);

        // 获取当前时间的小时、分钟、秒数
        let hours = now.getHours();
        let minutes = now.getMinutes();
        let seconds = now.getSeconds();

        // 处理小时数，将其转换为 24 小时制
        hours = hours < 10 ? '0' + hours : hours;
        if (hours === '00') hours = '24';

        // 格式化时间（保证小时、分钟、秒数都是两位数）
        minutes = minutes < 10 ? '0' + minutes : minutes;
        seconds = seconds < 10 ? '0' + seconds : seconds;

        list.push(hours + ':' + minutes + ':' + seconds);
    }
    return list;
};

// 格式化时间
export function formatTime(date) {
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
}

// 需要获取当前时间向前推算的n个时间点，包含时分秒的信息
export function getPreviousTimesWithSeconds(count) {
    const now = new Date();
    const times = [];

    for (let i = 0; i < count; i++) {
        const newTime = new Date(now.getTime() - i * 1000); // 减去i秒的毫秒数
        times.push(formatTime(newTime));
    }

    return times.sort();
}

// 时间格式计算
export function addSecondsToTime(initialTime, secondsToAdd) {
    // 将初始时间解析为小时、分钟和秒
    const [hours, minutes, seconds] = initialTime.split(':').map(Number);

    // 计算总秒数
    const totalSeconds = hours * 3600 + minutes * 60 + seconds + secondsToAdd;

    // 计算新的时间
    const newHours = Math.floor(totalSeconds / 3600);
    const remainingSeconds = totalSeconds % 3600;
    const newMinutes = Math.floor(remainingSeconds / 60);
    const newSeconds = remainingSeconds % 60;

    // 格式化为00:00:00格式
    const formattedTime = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}:${String(newSeconds).padStart(2, '0')}`;

    return formattedTime;
}

// 判断类型
export function typeOf(type, val) {
    if (val && val != '') {
        return Object.prototype.toString.call(val) === '[object ' + type + ']';
    }
};

// 判断参数是否是其中之一
export function oneOf(value, validList) {
    for (let i = 0; i < validList.length; i++) {
        if (value === validList[i]) {
            return true;
        }
    }
    return false;
}

/**
 * 将对象序列化转化成字符串
 * 如 {a:123, b: 234} => a=123&b=234
 */
export function objectToQueryString(obj) {
    const pairs = [];
    for (const [key, value] of Object.entries(obj)) {
        pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    }
    return pairs.join('&');
}

/**
 * grid 布局算法判断borad列数能否被12，15整除，可以的话返回12 或者15，不行的话返回他本身
 */
export function isDivisible(num) {
    if (num % 12 === 0) {
        return 12;
    } else if (num % 15 === 0) {
        return 15;
    } else {
        return num;
    }
}

// 将数组转对象
export function getObjByArray(arr, key, tkey) {
    const data = {};
    if (tkey) {
        Array.isArray(arr) && arr.forEach(ele => {
            if (ele?.[key]?.[tkey]) data[ele[key][tkey]] = ele;
        });
    } else {
        Array.isArray(arr) && arr.forEach(ele => {
            if (ele?.[key]) data[ele[key]] = ele;
        });
    }
    return data;
}

export function loopAllData(fileData, callback){
    const group = Array.isArray(fileData) ? fileData : [];

    let allGroup = [...group];
    let groupInfo;
    // eslint-disable-next-line no-cond-assign
    while (groupInfo = allGroup.shift()) {
        /**
         * 遍历子集
        */
        if (groupInfo?.children?.length) {
            const children = groupInfo.children;
            allGroup = children.concat(allGroup);
        }

        /**
         * 执行回调，判断是否需要跳出循环
         */
        const res = callback && callback(groupInfo, fileData);
        if (res === 0) return;
    }
}

/**
 * @description: 字符串大小写字母数计算方法
 * @param {string} string 字符串
 * @return {array} [大写字母数, 小写字母数]
 */
export const stringLetter = (string) => {
    const totalLength = string.length;
    let Acount = 0;
    for (let i = 0; i < totalLength; i++) {
        const char = string[i].charCodeAt();
        if (char >= 65 && char <= 90) {
            Acount++;
        }
    }
    const acount = totalLength - Acount;
    return [Acount, acount];
};

/**
 * @description: 通过span标签定位字符长度
 * @param {String} str 字符串
 * @return {Number} 返回字符长度
 */
export const getSpanWidth = (str, fontSize = '12px') => {
    let width = 0;
    const html = document.createElement('span');
    html.innerText = str;
    html.className = 'getTextWidth';
    html.style.fontSize = fontSize;
    document.querySelector('body').appendChild(html);
    width = document.querySelector('.getTextWidth').offsetWidth;
    document.querySelector('.getTextWidth').remove();
    return width;
};

/**
 * @description: 判断
 * @param {string} str 字符串
 * @return {array} [大写字母数, 小写
 */
export const getByteSize = (str) => {
    const encoder = new TextEncoder();
    const encoded = encoder.encode(str);
    return encoded.length;
};

/**
 * @description: 表记录结构体平铺方法
 * @param {object} object 单条表记录
 * @param {string} prefix 前缀
 * @return {object} 平铺结构体后的表记录
*/
export function flatStruct(object, prefix) {
    let res = {};
    Object.entries(object).forEach(item => {
        const [k, v] = item;
        const key = prefix ? `${prefix}：${k}` : k;
        if (typeof v !== 'object' || Array.isArray(v)) {
            res[key] = v;
        } else {
            res = Object.assign(res, flatStruct(v, key));
        }
    });
    return res;
}

// 返回值转换
export function transferVal(val){
    if (val === null ||  val === undefined || val === '' || val === '-' || val.toString() === 'NaN') {
        return false;
    }
    return val.toString();
}

/**
 * 将数字按百分比输出，避免js计算乘法
 * @param {Number | String} number 要转换的值
 * @param {Number?} dotLength - 保留小数点后几位，默认2
 * @returns {String}
 */
export function formatPercent(number, dotLength = 2){
    if (number === null ||  number === undefined || number === Infinity || number === '' || number === '-' || isNaN(number.toString())) {
        return '';
    } else if (number === 0) {
        return '0%';
    }
    return (Number(number) * 100).toFixed(dotLength) + '%';
    // const numberString = number.toString();
    // if (Number(number) >= 1) {
    //     return `${Number(number)}00%`;
    // }
    // // eslint-disable-next-line no-unused-vars
    // const [_, value] = numberString.split('.');
    // if (value.length <= 2) {
    //     return `${Number(value)}%`;
    // }
    // let res = '';
    // for (let i = 0; i < value.length; i++) {
    //     res += value[i];
    //     if (i === 1) {
    //         res += '.';
    //     }
    //     if (res.length >= 3 + dotLength) break;
    // }
    // return res + '%';
}

// 将数字转为科学计数法，保留一位小数
export function shortNumber(number, digits = 1) {
    const num = Math.abs(number);
    if (num > 9999) {
        // logaB / logaC = logcB 所以p就是10的幂
        const p = Math.floor(Math.log(num) / Math.LN10);
        const n = parseFloat((num * (10 ** -p)).toFixed(digits));
        return number > 0 ? `${n}${p === 0 ? '' : `e${p}`}` : `-${n}${p === 0 ? '' : `e${p}`}`;
    } else {
        return parseFloat(number.toFixed(digits));
    }
};

// 对象数组排序方法 - 从大到小
export function compareObjArr(prop) {
    return function (obj1, obj2) {
        const a = obj1[prop];
        const b = obj2[prop];
        if (isNaN(a) || a === undefined) {
            return 1;
        }
        if (isNaN(b) || b === undefined) {
            return -1;
        }
        return b - a;
    };
}

/**
 * 计算两个时间点之间的时间间隔（秒）
 * @param {string} currentTime 当前时间 (HH:mm:ss)
 * @param {string} previousTime 前次时间 (HH:mm:ss)
 * @returns {number} 时间间隔（秒，始终非负）
 */
export function calculateTimeDifference(currentTime, previousTime) {
    // 1. 时间字符串转秒数
    const timeToSeconds = (timeStr) => {
        const [h, m, s] = timeStr.split(':').map(Number);

        // 验证时间有效性
        if (h > 23 || m > 59 || s > 59 || [h, m, s].some(isNaN)) {
            throw new Error(`无效时间格式: ${timeStr}`);
        }
        return h * 3600 + m * 60 + s;
    };

    // 2. 转换为秒数
    const prevSecs = timeToSeconds(previousTime);
    const currSecs = timeToSeconds(currentTime);

    // 3. 计算基础时间差
    let interval = currSecs - prevSecs;

    // // 4. 处理跨天场景（智能修正）
    const SECONDS_PER_DAY = 86400;
    if (interval < 0) {
        interval += SECONDS_PER_DAY; // 仅当负值时添加全天秒数
    }

    // 5. 处理边界情况
    if (interval <= 0) {
        console.warn(`时间间隔异常: ${previousTime} → ${currentTime}, 使用默认1秒`);
        return 1; // 防止除零错误
    }

    // 6. 限制最大间隔（24小时）
    return Math.min(interval, SECONDS_PER_DAY);
}

// 格式化数据 避免四舍五入
export function formatNumTruncateDecimals(number, digits = 2) {
    const multiplier = Math.pow(10, digits);
    const truncated = Math.floor(number * multiplier) / multiplier;
    return truncated;
}

/**
 * 格式化数字
 * @param {Num} num
 * @return {String}
 */
export function formatNumber(num, digits = 2) {
    if (!transferVal(num) || isNaN(num)) return '';
    if (num >= 1e12){
        return parseFloat((num / 1e12).toFixed(digits)) + '万亿';
    } else if (num >= 1e8){
        return parseFloat((num / 1e8).toFixed(digits)) + '亿';
    } else if (num >= 10000) {
    // 大于等于10000时，将数字除以10000，并保留两位小数，然后添加 'w' 后缀
        return parseFloat((num / 10000).toFixed(digits)) + 'w';
    } else if (num >= 1000) {
    // 大于等于1000时，将数字除以1000，并保留两位小数，然后添加 'k' 后缀
        return parseFloat((num / 1000).toFixed(digits)) + 'k';
    } else {
    // 不满足以上条件时，返回原始数字
        return parseFloat((num / 1).toFixed(digits)).toString();
    }
}

/**
 * 数字单位自动转换-保留全部小数位
 * @param {Num} num
 * @return {String}
 */
export function formatNumberFloat(num) {
    if (!transferVal(num) || isNaN(num)) return '';
    const units = [
        { value: 1e12, unit: '万亿' },
        { value: 1e8, unit: '亿' },
        { value: 1e4, unit: '万' }
    ];
    for (let i = 0; i < units.length; i++) {
        if (num >= units[i].value) {
            return parseFloat(num / units[i].value) + units[i].unit;
        }
    }
    return cutZero(parseFloat(num).toString());
}

/**
 * 计算机计量单位自动转换
 * @param {Num} num
 * @return {String}
 */
export function formatComputerNumber(num) {
    if (!transferVal(num) || isNaN(num)) return '';
    const units = [
        { value: 2 ** 40, unit: 'TB' },
        { value: 2 ** 30, unit: 'GB' },
        { value: 2 ** 20, unit: 'MB' },
        { value: 2 ** 10, unit: 'KB' }
    ];
    for (let i = 0; i < units.length; i++) {
        if (num >= units[i].value) {
            return parseFloat((num / units[i].value)).toFixed(2) + units[i].unit;
        }
    }
    return parseFloat(num).toFixed(2) + 'B';
}

/**
 * 带宽单位自动转换
 * @param {Num} num
 * @return {String}
 */
export function formatBpsNumber(num) {
    if (!transferVal(num) || isNaN(num)) return '';
    const units = [
        { value: 2 ** 40, unit: ' Tbps' },
        { value: 2 ** 30, unit: ' Gbps' },
        { value: 2 ** 20, unit: ' Mbps' },
        { value: 2 ** 10, unit: ' kbps' }
    ];
    for (let i = 0; i < units.length; i++) {
        if (num >= units[i].value) {
            return parseFloat(num / units[i].value).toFixed(2) + units[i].unit;
        }
    }
    return parseFloat(num).toFixed(2) + ' bps';
}

/**
 * 百分比、千分比、万分比单位自动转换
 * @param {Num} num
 * @return {String}
 */
export function formatPercentNumber(num) {
    if (!transferVal(num) || isNaN(num)) return '';
    if (Number(num) === 0) {
        return '0%';
    } else if (num < 0.0001) {
        return (Number(num) * 10000).toFixed(2) + '‱';
    } else if (num < 0.001) {
        return (Number(num) * 1000).toFixed(2) + '‰';
    }
    return (Number(num) * 100).toFixed(2) + '%';
}

/**
 * 自动转换时间单位
 */
export function autoConvertTime(value, unit = 'ns') {
    const units = { ns: 1, μs: 1e3, ms: 1e6, s: 1e9, min: 6e10, h: 3.6e12, d: 8.64e13  };
    const unitsList = ['ns', 'μs', 'ms', 's', 'min', 'h', 'd'];
    if (!(unit in units)) {
        throw new Error("Invalid time unit. Use 'ns', 'μs', 'ms', 's', 'min', 'h', or 'd'.");
    }
    const index = unitsList.indexOf(unit);
    let resVal = value || 0;
    let resUnit = unit;
    for (let i = index; i < unitsList.length - 1; i++) {
        const currentUnit = unitsList[i];
        const nextUnit = unitsList[i + 1];

        if (resVal < (units[nextUnit] / units[currentUnit])) {
            return { value: parseFloat((resVal / 1).toFixed(2)), unit: currentUnit };
        }

        resVal = resVal * units[currentUnit] / units[nextUnit] || 0;
        resUnit = nextUnit;
    }
    return { value: parseFloat(resVal.toFixed(2)), unit: resUnit };
}

// 转换为最合适的单位（B、KB、MB、GB）
export function convertToBestUnit(value = 0, unit = 'MB') {
    const units = { B: 1, KB: 1024, MB: 1024 ** 2, GB: 1024 ** 3 };

    try {
        // 将输入值转换为字节
        const bytesValue = parseFloat(value) * units[unit.toUpperCase()];

        // 选择最合适的单位
        for (const [unitName, unitValue] of Object.entries(units)) {
            if (bytesValue < unitValue * 1024 || unitName === 'GB') {
                return { value: parseFloat((bytesValue / unitValue).toFixed(2)), unit: unitName };
            }
        }
    } catch (error) {
        return { value: parseFloat((value / 1).toFixed(2)), unit: unit };
    }
}

// 数字、大写小写字母、汉字排序
export function customSort(arr) {
    return arr?.length ? arr.sort(function (a, b) {
        if (typeof a === 'number' && typeof b !== 'number') {
            return -1;
        } else if (typeof a !== 'number' && typeof b === 'number') {
            return 1;
        }

        // 将所有元素转换为小写字母以进行不区分大小写的排序
        // eslint-disable-next-line no-param-reassign
        a = a.toString().toLowerCase();
        // eslint-disable-next-line no-param-reassign
        b = b.toString().toLowerCase();

        // 使用localeCompare方法进行汉字排序
        if (/[\u4e00-\u9fa5]/.test(a) && /[\u4e00-\u9fa5]/.test(b)) {
            return a.localeCompare(b, 'zh-Hans-CN', { sensitivity: 'base' });
        }

        if (a < b) {
            return -1;
        } else if (a > b) {
            return 1;
        } else {
            return 0;
        }
    }) : [];
}

/**
 * 解析SQL字符
 * @param { String } sqlStr
 * @return { Array }
 */
export function getSqlListFromSqlStr(sqlStr) {
    const regex = /\/\*[\s\S]*?\*\/|--.*?$|^\s*$/gm;
    const sql = sqlStr.replace(regex, '');
    const sqlList = sql.split(';').filter(item => item.trim());
    return sqlList.map(item => item.trim() + ';');
}

/**
 * 时间加减计算
 * @param { String } inputTime
 * @param { Number } secondsToAddOrSubtract
 * @return { String } outputTime
 */
export function manipulateTime(inputTime, secondsToAddOrSubtract) {
    // 将传递的时间字符串解析为 Date 对象
    const currentTime = new Date('1970-01-01T' + inputTime + 'Z');

    // 添加或减去秒数
    currentTime.setSeconds(currentTime.getSeconds() + secondsToAddOrSubtract);

    // 获取新的时间并格式化为 "00:00:00"
    const newHours = currentTime.getUTCHours().toString().padStart(2, '0');
    const newMinutes = currentTime.getUTCMinutes().toString().padStart(2, '0');
    const newSeconds = currentTime.getUTCSeconds().toString().padStart(2, '0');

    // 返回格式化后的时间字符串
    return newHours + ':' + newMinutes + ':' + newSeconds;
}

/**
 * 计算对象数组中某个字段累加值
 * @param { Array } data 对象数组
 * @param { String } key 需累加的字段key
 * @return { String } 累加值
 */
export function sumObjKey(data, key) {
    let sumCount = 0;
    Array.isArray(data) && data.forEach(item => {
        sumCount += item?.[key] || 0;
    });
    return sumCount;
}

// ns值格式化时间+单位
export function chartConvertTime (value, unit = 'ns') {
    const res = transferVal(value) ? autoConvertTime(value, unit) : '';
    const convertVal = res ? res?.value + ' ' + res?.unit : '';
    return convertVal || '-';
}

// ns值格式化时间+单位
export function nsConvertTime(value) {
    const res = transferVal(value) ? autoConvertTime(value) : '';
    const convertVal = res ? res?.value + ' ' + res?.unit : '';
    return convertVal || '-';
}

// 下划线转换为小驼峰命名
export function underscoreToCamelCase(str) {
    return str.replace(/_([a-z])/g, function (match, letter) {
        return letter.toUpperCase();
    });
}

/**
 * 通用延迟
 */
export function sleep(time = 500) {
    if (sleep?.timer) {
        clearTimeout(sleep.timer);
    }
    return new Promise(resolve => {
        sleep.timer = setTimeout(() => {
            resolve();
        }, time);
    });
}

/**
 * 尝试获取select sql语句的ast结构
 * @param {string} sql
 * @return {object}}
 */
export function tryGetSqlSelectAst(sql) {
    const prefix = sql.replace(/\n|\t| /g, '')?.slice(0, 6)?.toLowerCase?.();
    if (prefix === 'select') {
        return {
            type: 'select',
            from: [{
                table: '<table_name>'
            }]
        };
    }
    return {};
}

/**
 * 尝试获取sql语句的前缀
 * @param {string} sql
 * @return {object}}
 */
export function tryGetSqlPretix(sql) {
    const prefix = sql.replace(/\n|\t| /g, '')?.slice(0, 6)?.toLowerCase?.();
    return prefix;
}

/**
 * 图表坐标数字单位自动转换
 * @param {Num} num
 * @return {String}
 */
export function formatChartNumber(num) {
    if (!transferVal(num) || isNaN(num)) return '';
    const units = [
        { value: 1e12, unit: '万亿' },
        { value: 1e8, unit: '亿' },
        { value: 1e4, unit: '万' }
    ];
    for (let i = 0; i < units.length; i++) {
        if (num >= units[i].value) {
            return cutZero((num / units[i].value).toFixed(2)) + units[i].unit;
        }
    }
    return cutZero(parseFloat(num).toFixed(2));
}

// 表格ns值格式化时间+单位
// eslint-disable-next-line max-params
export function autoConvertTimeRender(h, params, key, unit = 'ns') {
    const res = transferVal(params?.row?.[key]) ? autoConvertTime(params.row[key], unit) : '';
    const value = res ? res?.value + ' ' + res?.unit : '-';
    return h('span', {
        attrs: {
            title: value
        }
    }, value);
}

/**
 * 导入图标字体库
 * @param vue vue实例
 */
export function importFontFace(vue) {
    const fontUrls = {
        truetype: `${vue.IMG_HOME}static/fonts/codicon.ttf`
    };

    // 为每种字体格式创建一个FontFace对象
    Object.keys(fontUrls).forEach(format => {
        const fontFace = new FontFace('codicon', `url(${fontUrls[format]})`, {
            weight: 'normal',
            style: 'normal'
        });

        // 加载字体
        fontFace.load().then(font => {
            // 将字体添加到 document 的字体族
            document.fonts.add(font);
        }).catch(error => {
            console.error(`Error loading font format ${format}:`, error);
        });
    });
}

/**
 * 导入图标字体库
 * @param {String}
 * @return {String}
 */
export function sanitizeFilename(filename) {
    // 用下划线替换非法字符
    // eslint-disable-next-line no-useless-escape
    const sanitized = filename.replace(/[\/\\?*:|"<>]/g, '_');
    // 移除文件名两端的空格
    return sanitized.trim();
}

/**
 * 对比两个数组对象是否一致
 */
export function areObjectArraysEqualUnordered(arr1, arr2) {
    // 如果长度不同，直接返回 false
    if (arr1.length !== arr2.length) {
        return false;
    }

    // 将数组中的对象转为字符串进行排序后比较
    const sortedArr1 = arr1.map(obj => JSON.stringify(obj)).sort();
    const sortedArr2 = arr2.map(obj => JSON.stringify(obj)).sort();

    // 判断排序后的数组是否一致
    return sortedArr1.every((objStr, index) => objStr === sortedArr2[index]);
}

/**
 * 自定义日期格式化
 * @param {String | Number | Date} date
 * @param {String} format
 * @returns {String}
 */
export function dateFormat(date, format = 'YYYY-MM-DD HH:mm:ss') {
    const d = new Date(date);

    if (d.toString().toLowerCase() === 'invalid date') return '';

    const pad = n => n.toString().padStart(2, '0');

    const map = {
        YYYY: d.getFullYear(),
        MM: pad(d.getMonth() + 1),
        DD: pad(d.getDate()),
        HH: pad(d.getHours()),
        mm: pad(d.getMinutes()),
        ss: pad(d.getSeconds())
    };

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => map[match]);
}

/*
 * 字符串数组转数组
 * @param {string} str 字符串，支持多种格式：'a,b,c' 或 '["a","b","c"]'
 * @param {string} separator 分隔符，默认为逗号
 * @returns {Array} 转换后的数组
 */
export function stringToArray(str, separator = ',') {
    if (!str || typeof str !== 'string') {
        return [];
    }

    // 去除首尾空格
    const trimmedStr = str.trim();

    // 如果是JSON数组格式，直接解析
    if (trimmedStr.startsWith('[') && trimmedStr.endsWith(']')) {
        try {
            const parsed = JSON.parse(trimmedStr);
            return Array.isArray(parsed) ? parsed : [];
        } catch (e) {
            console.warn('JSON解析失败:', e);
            return [];
        }
    }

    // 如果是普通字符串，按分隔符分割
    if (separator === ',') {
        // 处理逗号分隔的情况，支持引号包裹的字符串
        const matches = trimmedStr.match(/([^,]+)/g);
        if (matches) {
            return matches.map(item => item.trim().replace(/^["']|["']$/g, ''));
        }
    } else {
        // 其他分隔符直接分割
        return trimmedStr.split(separator).map(item => item.trim()).filter(item => item);
    }

    return [];
}

// string转object方法
export function stringToObject(str) {
    try {
        if (!str || typeof str !== 'string') {
            return {};
        }

        // 去除首尾空格
        const trimmedStr = str.trim();
        // 尝试解析JSON字符串
        if (trimmedStr.startsWith('{') && trimmedStr.endsWith('}')) {
            return JSON.parse(trimmedStr);
        }
        // 尝试解析普通对象字符串格式，如 "key1:value1,key2:value2"
        const obj = {};
        const pairs = trimmedStr.split(',');
        pairs.forEach(pair => {
            const [key, value] = pair.split(':').map(item => item.trim());
            if (key) {
                // 保持原始值，不做类型转换
                obj[key] = value;
            }
        });
        return obj;
    } catch (error) {
        console.warn('字符串转对象失败:', error);
        return str; // 如果转换失败，返回原字符串
    }
}

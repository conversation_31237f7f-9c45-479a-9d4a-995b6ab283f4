<!--
 * <AUTHOR>
 * @email <EMAIL>
 * @create date 2024-12-10 11:05:42
 * @modify date 2024-12-10 11:05:42
 * @desc 核心功能号处理页面
 */
-->
<template>
    <div class="main">
        <header>
            <a-title title="核心功能号处理">
                <slot>
                    <h-select
                        v-show="productList.length > 1"
                        v-model="productInstNo"
                        class="title-single-select"
                        placeholder="请选择"
                        :positionFixed="true"
                        :clearable="false"
                        @on-change="checkProduct">
                        <h-option
                            v-for="item in productList"
                            :key="item.id"
                            :value="item.productInstNo"
                            >{{ item.productName }}</h-option>
                    </h-select>
                </slot>
            </a-title>
        </header>
        <div v-if="isLoadingCategory" class="main-loading-wraper">
            <a-loading/>
        </div>
        <div v-else class="content">
            <div v-if="tabs.length > 0" class="content-top" >
                <div
                    class="content-top-tab"
                    :style="{width: `calc(100% - ${operationWidth})`}"
                    :data-showarrow="showCategoryArrow"
                    :data-loading="isLoadingFuncList"
                >
                    <h-tabs
                        ref="tabs"
                        v-model="tabId"
                        :showArrow="showCategoryArrow"
                        :animated="false"
                        @on-click="handleTabChange"
                    >
                        <h-tab-pane
                            v-for="item in tabs"
                            :key="item.id"
                            :disabled="item.disabled"
                            :name="item.id"
                            :label="item.name"
                        />
                    </h-tabs>
                </div>
                <div ref="operation" class="content-top-operation">
                    <h-select
                        v-model="shardingNo"
                        class="title-single-select"
                        placeholder="请选择分片"
                        :positionFixed="true"
                        :disabled="isLoadingCategory || isLoadingFuncList || isLoadingShardList"
                        :clearable="true"
                        @on-change="onChangeShardingNo">
                        <h-option
                            v-for="item in shardingList"
                            :key="item.shardingNo"
                            :value="item.shardingNo"
                            >{{ item.shardingName }}</h-option>
                    </h-select>
                    <h-select
                        v-model="clusterId"
                        class="title-single-select"
                        placeholder="请选择集群"
                        :positionFixed="true"
                        :disabled="isLoadingCategory || isLoadingFuncList || isLoadingClusterList"
                        :clearable="true"
                        @on-change="onChangeClusterId">
                        <h-option
                            v-for="item in clusterList"
                            :key="item.id"
                            :value="item.id"
                            >{{ item.clusterName }}</h-option>
                    </h-select>
                    <div class="content-top-operation-line" />
                    <div class="content-top-operation-type">
                        <div class="content-top-operation-type-item" :data-current="showModel==='card'" @click="() => changetype('card')">card</div>
                        <div class="content-top-operation-type-item" :data-current="showModel==='table'" @click="() => changetype('table')">table</div>
                    </div>
                    <div
                        :style="{cursor: settingDisabled ? 'not-allowed': 'pointer' }"
                        class="content-top-operation-setting"
                        @click="openSetting">
                        <h-icon name="setup" />
                    </div>
                </div>
            </div>
            <div v-if="cardList.length > 0" ref="tab-box" class="content-wrap">
                <div v-if="showModel === 'card'" class="content-list" :data-big-than-two="cardList.length >= 2 ? 'true' : 'false'">
                    <div v-for="(card, index) in cardList"
                        :key="`${card.title}${card.subTitle}${card.subTitle2}`"
                        :data-single="card.length === 1"
                        class="content-list-row-item"
                        :data-is-third-count="mactchThirdIndex.includes(index + 1)"
                    >
                        <apm-row-card
                            :title="card.title"
                            :subTitle1="card.subTitle"
                            :subTitle2="card.subTitle2"
                            :list="card.list"
                        />
                    </div>
                </div>
                <a-table v-if="showModel === 'table'" showTitle class="content-wrap-table" :height="tableHeight" :hasPage="false" :columns="columns" :tableData="cardList"/>
            </div>
            <no-data v-if="cardList.length === 0" class="empty-funcs" />
            <div v-if="isFirstRender && isLoadingFuncList" class="main-loading-wraper">
                <a-loading/>
            </div>
        </div>
        <display-setting-drawer :drawerInfo="drawerInfo"/>
    </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import _ from 'lodash';
import aTitle from '@/components/common/title/aTitle';
import apmRowCard from '@/components/common/apmRowCard/apmRowCard';
import { queryDashbordsTags, queryDashbordsConfig, queryCoreFuncList } from '@/api/coreFunctionApi';
import { getProductClusters, getProductShardings } from '@/api/productApi';
import { formatCoreFuncValue } from './util';
import aLoading from '@/components/common/loading/aLoading';
import noData from '@/components/common/noData/noData';
import aTable from '@/components/common/table/aTable';
import displaySettingDrawer from './displaySettingDrawer.vue';
import './index.less';

export default {
    components: { aTitle, noData, apmRowCard, aLoading, displaySettingDrawer, aTable  },
    data() {
        return {
            productInstNo: '',
            isLoadingFuncList: false,
            tabs: [],
            tabId: null,
            showModel: 'card',
            isLoadingCategory: false, // 正在获取分类信息
            clusterId: null, // 集群id
            clusterList: [], // 集群列表
            shardingNo: null, // 分片号
            shardingList: [], // 分片列表
            isFecthingList: false,
            isLoadingShardList: false,
            isLoadingClusterList: false,
            cardList: [],
            drawerInfo: {},
            operationWidth: '285px',
            showCategoryArrow: false,
            freshTimer: null,
            tableHeight: 0,
            isFirstRender: true,
            columns: [
                {
                    title: '功能号名称',
                    key: 'functionName',
                    ellipsis: true,
                    minWidth: 280,
                    className: 'core-func-left'
                },
                {
                    title: '分片',
                    key: 'subTitle',
                    ellipsis: true,
                    className: 'core-func-left',
                    width: 80
                },
                {
                    title: '集群',
                    key: 'subTitle2',
                    ellipsis: true,
                    className: 'core-func-left',
                    width: 80
                },
                {
                    title: '执行吞吐(tps)',
                    ellipsis: true,
                    key: 'throughput'
                    // width: 150
                },
                {
                    title: '平均执行耗时(ns)',
                    ellipsis: true,
                    key: 'avgLatency'
                    // width: 150
                },
                {
                    title: '错误次数(次)',
                    ellipsis: true,
                    key: 'errorNum'
                    // width: 150
                },
                {
                    title: '错误率(%)',
                    ellipsis: true,
                    key: 'errorRate'
                    // width: 150
                },
                {
                    title: '队列积压',
                    ellipsis: true,
                    key: 'queueBacklogNum'
                    // width: 150
                }
            ]
        };
    },
    async mounted() {
        await this.getProductList({ filter: 'excludeLdpApm' });
        window.addEventListener('resize', this.fetTableHeight);
        const productInstNo = localStorage.getItem('productInstNo');
        this.productInstNo =  _.find(this.productList, ['productInstNo', productInstNo])?.productInstNo ||
            this.productList?.[0]?.productInstNo;
        window.addEventListener('resize', this.onResize);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.onResize);
        window.removeEventListener('resize', this.fetTableHeight);
        clearTimeout(this.freshTimer);
    },
    computed: {
        ...mapState({
            productList: state => {
                return state.product.productListLight;
            }
        }),
        /**
         * 设置抽屉禁用状态
         */
        settingDisabled() {
            return this.isLoadingClusterList || this.isLoadingShardList;
        },
        mactchThirdIndex() {
            if (!this.cardList.length) return [];
            const result = [2];
            for (let i = 2; i <= this.cardList.length; i += 3) {
                result.push(i);
            }
            return result;
        }
    },
    methods: {
        ...mapActions({ getProductList: 'product/getProductListLight' }),
        // 切换产品
        async checkProduct(productId) {
            if (!this.productList.length) return;
            this.productInfo = productId ? _.find(this.productList, ['productInstNo', productId]) : this.productList[0];
            this.productInstNo = this.productInfo.productInstNo;
            this.resetAllInfo();
            localStorage.setItem('productInstNo', this.productInfo.productInstNo);
            clearTimeout(this.freshTimer);
            this.queryCategory(this.productInstNo);
        },
        /**
         * 重置分类、分片、集群信息
         */
        resetAllInfo() {
            this.tabs = [];
            this.tabId = null;
            this.shardingList = [];
            this.shardingNo = null;
            this.clusterId = null;
            this.clusterList = [];
        },
        changetype(type) {
            this.showModel = type;
        },
        // 设置table高度
        fetTableHeight() {
            this.tableHeight = this.$refs['tab-box']?.offsetHeight - 21;
        },
        /**
         * 监听窗口变化事件
         */
        onResize: _.throttle(function () {
            this.setTabArrowVisible();
        }, 40),
        /**
         * 设置tab宽度
         */
        setTabContentWidth() {
            this.$nextTick(() => {
                const operationDom = this.$refs['operation'];
                if (operationDom) {
                    const width = operationDom.getBoundingClientRect().width;
                    this.operationWidth = `${width}px`;
                }
            });
        },
        /**
         * 设置tabbar的箭头是否可见
         */
        setTabArrowVisible() {
            this.$nextTick(() => {
                const tabsParent = this.$refs['tabs']?.$el;
                if (tabsParent) {
                    const tabs = tabsParent.querySelector('.h-tabs-nav');
                    if (tabs) {
                        const width = tabs.getBoundingClientRect().width;
                        const freeWidth = window.innerWidth - 16 - Number(this.operationWidth.replace('px', ''));
                        this.showCategoryArrow = freeWidth < width;
                    }
                }
            });
        },
        /**
         * 查询分类信息
         */
        async queryCategory(productId) {
            try {
                this.cardList = [];
                this.tabs = [];
                this.isLoadingCategory = true;
                const tagsRes = await queryDashbordsTags({
                    scene: 'bizprocServiceObservation',
                    objectType: 'product',
                    objectId: productId
                });
                if (tagsRes?.success && tagsRes?.data?.length) {
                    const dashboardsRes = await queryDashbordsConfig({
                        scene: 'bizprocServiceObservation',
                        tags: tagsRes.data,
                        objectType: 'product',
                        objectId: productId
                    });
                    if (dashboardsRes?.success && dashboardsRes?.data?.length) {
                        this.tabs = dashboardsRes?.data.reduce((pre, item) => {
                            if (item.visible) {
                                return [...pre, {
                                    ...item,
                                    name: item.name,
                                    id: item.dashboardObjId
                                }];
                            }
                            return pre;
                        }, []);
                        this.$nextTick(() => {
                            this.handleTabChange(this.tabs[0]?.id);
                            this.setTabArrowVisible();
                        });
                    }
                }
            } catch (error) {
                console.log('查询分类失败', error);
            } finally {
                this.isLoadingCategory = false;
            }
        },
        // 切换tab页
        async handleTabChange(tabId) {
            this.tabId = tabId;
            this.shardingList = [];
            this.clusterList = [];
            this.shardingNo = null;
            this.clusterId = null;
            this.cardList = [];
            if (!tabId) return;
            this.isFirstRender = true;
            const { success } = await this.queryCoreFunction();
            this.$nextTick(() => {
                this.queryShardingInfo();
                this.queryClusterInfo();
            });
            if (success){
                this.startInterval();
            }
        },
        /**
         * start interval
         */
        startInterval() {
            clearTimeout(this.freshTimer);
            const { timerSwitch, timerInterval } = this.tabs.find(item => item.id === this.tabId) || {};
            if (timerSwitch) {
                this.callStartInterval(timerInterval);
            }
        },
        /**
         * 开始轮询
         */
        callStartInterval(timerInterval) {
            clearTimeout(this.freshTimer);
            this.freshTimer = setTimeout(async() => {
                const { success } = await this.queryCoreFunction();
                if (success){
                    this.callStartInterval(timerInterval);
                } else {
                    clearTimeout(this.freshTimer);
                }
            }, (timerInterval || 5) * 1000);
        },
        /**
         * 查询分片信息
         */
        async queryShardingInfo() {
            try {
                this.isLoadingShardList = true;
                const shardingRes = await getProductShardings({
                    productId: this.productInstNo,
                    bizSysType: this.tabId,
                    instanceIdentity: 'bizproc'
                });
                if (shardingRes?.data?.shardings?.length) {
                    this.shardingList = shardingRes.data.shardings;
                }
            } catch (error) {
                console.log('查询分片信息异常', error);
            } finally {
                this.isLoadingShardList = false;
            }
        },
        /**
         * 查询集群信息
         */
        async queryClusterInfo() {
            try {
                this.isLoadingClusterList = true;
                const clusterRes = await getProductClusters({
                    productId: this.productInstNo,
                    bizSysType: this.tabId,
                    instanceIdentity: 'bizproc'
                });
                if (clusterRes?.data?.appClusters?.length) {
                    this.clusterList = clusterRes.data.appClusters;
                }
            } catch (error) {
                console.log('查询集群信息', error);
            } finally {
                this.isLoadingClusterList = false;
            }
        },
        /**
         * 切换分片
         */
        async onChangeShardingNo(shardingNo) {
            this.shardingNo = shardingNo;
            this.setTabContentWidth();
            const { success } = await this.queryCoreFunction();
            if (success){
                this.startInterval();
            }
        },
        /**
         * 切换集群
         */
        async onChangeClusterId(clusterId) {
            clearTimeout(this.freshTimer);
            this.clusterId = clusterId;
            this.setTabContentWidth();
            const { success } = await this.queryCoreFunction();
            if (success){
                this.startInterval();
            }
        },
        /**
         * 打开设置
         */
        openSetting() {
            if (this.settingDisabled)  {
                this.$hMessage.info('分片、集群加载中');
                return;
            };
            const curTab = this.tabs.find(item => item.id === this.tabId);
            this.drawerInfo = {
                visible: true,
                productId: this.productInstNo,
                shardingList: this.shardingList,
                groupId: this.tabId,
                clusterList: this.clusterList,
                title: '功能号显示设置',
                categoryName: curTab?.name
            };
        },
        /**
         * 查询功能号列表
         */
        async queryCoreFunction() {
            if (this.isFecthingList) return;
            try {
                this.isFecthingList = true;
                this.isLoadingFuncList = true;
                this.tabs = this.tabs.map(item => ({
                    ...item,
                    disabled: true
                }));
                const param = {
                    productId: this.productInstNo,
                    serviceCode: this.tabId
                };
                if (this.shardingNo) {
                    param.shardingNo = this.shardingNo;
                }
                if (this.clusterId) {
                    param.clusterId = this.clusterId;
                }
                const res = await queryCoreFuncList(param);
                if (res?.success && res?.data) {
                    this.cardList = res.data.map(item => {
                        const throughput = formatCoreFuncValue(item.throughput);
                        const avgLatency = formatCoreFuncValue(item.avgLatency);
                        const errorNum = formatCoreFuncValue(item.errorNum);
                        const queueBacklogNum = formatCoreFuncValue(item.queueBacklogNum);
                        return  {
                            functionName: `${item.functionName ?? '-'}(${item.functionNo})`,
                            title: `${item.functionName ?? '-'}(${item.functionNo})`,
                            subTitle: item.shardingName ?? '-',
                            subTitle2: item.clusterName ?? '-',
                            errorRate: item.errorRate ?? '-',
                            queueBacklogNum,
                            throughput,
                            avgLatency,
                            errorNum,
                            list: [
                                {
                                    title: '执行吞吐(tps)',
                                    value: throughput
                                },
                                {
                                    title: `平均执行耗时(ns)`,
                                    value: avgLatency
                                },
                                {
                                    title: '错误次数(次)',
                                    value: errorNum
                                },
                                {
                                    title: '错误率(%)',
                                    value: item.errorRate ?? '-'
                                },
                                {
                                    title: '队列积压',
                                    value: queueBacklogNum
                                }
                            ]
                        };
                    });
                    return { success: true };
                }
                return { success: false };
            } catch (error) {
                console.log('查询功能号异常', error);
                return { success: false };
            } finally {
                this.isFecthingList = false;
                this.isLoadingFuncList = false;
                this.tabs = this.tabs.map(item => ({
                    ...item,
                    disabled: false
                }));
                if (this.isFirstRender) {
                    this.isFirstRender = false;
                }
            }
        }
    }
};
</script>

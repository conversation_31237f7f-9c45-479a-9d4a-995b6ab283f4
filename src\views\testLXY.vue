<template>
  <div class="main">
    <ve-table
        fixed-header
        :scroll-width="1600"
        :max-height="500"
        border-y
        :columns="columns"
        :table-data="tableData"
        rowKeyFieldName="rowKey"
        :virtual-scroll-option="virtualScrollOption"
        :cell-autofill-option="cellAutofillOption"
        :rowStyleOption="rowStyleOption" />
  </div>
</template>

<script>
// import 'vue-easytable/libs/theme-default/index.css';
// import { VeTable } from 'vue-easytable';
export default {
    name: 'ProductTimeDetail',
    components: { VeTable },
    data() {
        return {
            rowStyleOption: {
                clickHighlight: false,
                hoverHighlight: false
            },
            virtualScrollOption: {
                // 是否开启
                enable: true
            },
            cellAutofillOption: {
                directionX: true,
                directionY: true,
                beforeAutofill: ({
                    direction,
                    sourceSelectionRangeIndexes,
                    targetSelectionRangeIndexes,
                    sourceSelectionData,
                    targetSelectionData
                }) => {
                    console.log('direction::', direction);
                    console.log('sourceSelectionRangeIndexes::', sourceSelectionRangeIndexes);
                    console.log('targetSelectionRangeIndexes::', targetSelectionRangeIndexes);
                    console.log('sourceSelectionData::', sourceSelectionData);
                    console.log('targetSelectionData::', targetSelectionData);
                    console.log('---');
                },
                afterAutofill: ({
                    direction,
                    sourceSelectionRangeIndexes,
                    targetSelectionRangeIndexes,
                    sourceSelectionData,
                    targetSelectionData
                }) => {
                    console.log('direction::', direction);
                    console.log('sourceSelectionRangeIndexes::', sourceSelectionRangeIndexes);
                    console.log('targetSelectionRangeIndexes::', targetSelectionRangeIndexes);
                    console.log('sourceSelectionData::', sourceSelectionData);
                    console.log('targetSelectionData::', targetSelectionData);
                    console.log('---');
                }
            },
            columns: [
                {
                    field: 'index',
                    key: 'index',
                    // is operation column
                    operationColumn: true,
                    title: '#',
                    width: 15,
                    fixed: 'left',
                    sortBy: ''
                },
                {
                    field: 'col1',
                    key: 'a',
                    title: 'col1',
                    width: 50,
                    fixed: 'left'
                },
                {
                    title: 'col2-col3',
                    fixed: 'left',
                    children: [
                        {
                            field: 'col2',
                            key: 'b',
                            title: 'col2',
                            width: 50,
                            sortBy: ''
                        },
                        {
                            field: 'col3',
                            key: 'col3',
                            title: 'col3',
                            width: 30
                        }
                    ]
                },
                {
                    title: 'col4-col5-col6',
                    children: [
                        {
                            title: 'col4-col5',
                            children: [
                                {
                                    field: 'col4',
                                    key: 'col4',
                                    title: 'col4',
                                    width: 110
                                },
                                {
                                    field: 'col5',
                                    key: 'col5',
                                    title: 'col5',
                                    width: 120
                                }
                            ]
                        },
                        {
                            title: 'col6',
                            field: 'col6',
                            key: 'col6',
                            width: 130
                        }
                    ]
                },
                {
                    title: 'col7',
                    fixed: 'right',
                    children: [
                        {
                            title: 'col7-1',
                            field: 'col7',
                            key: 'col7',
                            width: 50
                        }
                    ]
                }
            ],
            tableData: []
        };
    },
    methods: {
        initTableData() {
            const data = [];
            for (let i = 0; i < 100; i++) {
                data.push({
                    rowKey: i,
                    index: i + 1,
                    col1: `A${i + 1}`,
                    col2: `B${i + 1}`,
                    col3: `C${i + 1}`,
                    col4: `D${i + 1}`,
                    col5: `E${i + 1}`,
                    col6: `F${i + 1}`,
                    col7: `G${i + 1}`
                });
            }
            this.tableData = data;
        }
    },
    created() {
        this.initTableData();
    }
};

</script>
<style>
/* @import "../font/iconfont.css"; */
/* ve-table */
/* ve-pagination */
/* ve-checkbox */
/* ve-radio */
/* ve-select */
/* ve-dropdown */
/* ve-contextmenu */
.ve-checkbox {
  cursor: pointer;
  font-size: 12px;
  display: inline-block;
  position: relative;
  vertical-align: middle;
}
.ve-checkbox:hover .ve-checkbox-inner {
  border-color: #108ee9;
}
.ve-checkbox-content {
  white-space: nowrap;
  cursor: pointer;
  outline: none;
  display: inline-block;
  line-height: 1;
  position: relative;
  vertical-align: text-bottom;
}
.ve-checkbox-content:hover .ve-checkbox-inner {
  border-color: #108ee9;
}
.ve-checkbox-content .ve-checkbox-input {
  position: absolute;
  left: 0;
  z-index: 1;
  cursor: pointer;
  opacity: 0;
  filter: alpha(opacity=0);
  top: 0;
  bottom: 0;
  right: 0;
}
.ve-checkbox-content .ve-checkbox-input:focus .ve-checkbox-inner {
  border-color: #108ee9;
}
.ve-checkbox-content .ve-checkbox-inner {
  position: relative;
  top: 0;
  left: 0;
  display: block;
  width: 16px;
  height: 16px;
  border: 1px solid #abbacc;
  border-radius: 2px;
  background-color: #fff;
  transition: all 0.3s;
}
.ve-checkbox-content .ve-checkbox-inner:after {
  transform: rotate(45deg) scale(0);
  position: absolute;
  left: 4px;
  top: 2px;
  display: table;
  width: 6px;
  height: 9px;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
  content: " ";
  transition: all 0.1s cubic-bezier(0.71, -0.46, 0.88, 0.6);
}
.ve-checkbox .ve-checkbox-checked:after {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 2px;
  border: 1px solid #108ee9;
  content: "";
  animation-fill-mode: both;
  visibility: hidden;
}
.ve-checkbox .ve-checkbox-checked .ve-checkbox-inner {
  background-color: #108ee9;
  border-color: #108ee9;
}
.ve-checkbox .ve-checkbox-checked .ve-checkbox-inner:after {
  transform: rotate(45deg) scale(1);
  position: absolute;
  display: table;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
  content: " ";
  transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
}
.ve-checkbox .ve-checkbox-indeterminate .ve-checkbox-inner {
  border-color: #d9d9d9;
  position: relative;
  top: 0;
  left: 0;
  direction: ltr;
  background-color: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  border-collapse: separate;
  transition: all 0.3s;
}
.ve-checkbox .ve-checkbox-indeterminate .ve-checkbox-inner:after {
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background-color: #108ee9;
  border: 0;
  -webkit-transform: translate(-50%, -50%) scale(1);
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
  content: " ";
}
.ve-checkbox .ve-checkbox-indeterminate .ve-checkbox-inner:hover {
  border-color: #108ee9;
}
.ve-checkbox .ve-checkbox-indeterminate.ve-checkbox-disabled .ve-checkbox-inner:after {
  border-color: rgba(0, 0, 0, 0.25);
}
.ve-checkbox .ve-checkbox-disabled {
  cursor: not-allowed;
}
.ve-checkbox .ve-checkbox-disabled.ve-checkbox-checked .ve-checkbox-inner:after {
  animation-name: none;
  border-color: rgba(0, 0, 0, 0.25);
}
.ve-checkbox .ve-checkbox-disabled .ve-checkbox-input {
  cursor: not-allowed;
}
.ve-checkbox .ve-checkbox-disabled .ve-checkbox-inner {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  border-color: #d9d9d9 !important;
  background-color: #f7f7f7;
}
.ve-checkbox .ve-checkbox-disabled .ve-checkbox-inner:after {
  animation-name: none;
  border-color: #f7f7f7;
}
.ve-checkbox-label {
  margin: 0 6px 0 3px;
  width: 100%;
  color: #000000d9 !important;
}
.ve-contextmenu-popper {
  z-index: 9999;
  overflow: hidden;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 #0000001a;
}
.ve-contextmenu {
  display: flex;
  flex-direction: row;
}
.ve-contextmenu-panel {
  min-width: 180px;
  min-height: 50px;
  overflow: hidden;
}
.ve-contextmenu-panel .ve-contextmenu-list {
  min-height: 100%;
  margin: 0;
  padding: 6px 0;
  list-style: none;
  box-sizing: border-box;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node {
  display: flex;
  align-items: center;
  padding: 0 30px 0 20px;
  height: 34px;
  justify-content: center;
  outline: none;
  color: #000000d9;
  cursor: pointer;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node:not(.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-disabled):hover {
  background: #f5f7fa;
  color: #409eff;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-active {
  background: #f5f7fa;
  color: #409eff;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-label {
  flex: 1;
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-icon-postfix {
  color: #666;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-disabled {
  color: #c0c4cc;
  cursor: not-allowed;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-disabled .ve-contextmenu-node-icon-postfix {
  color: #c0c4cc;
}
.ve-contextmenu-panel .ve-contextmenu-list .ve-contextmenu-node-separator {
  height: 1px;
  margin: 5px 0px;
  border-bottom: 1px solid #eee;
}
.ve-dropdown {
  display: inline-table;
  margin: 0;
}
.ve-dropdown a,
.ve-dropdown a:visited {
  color: #000;
  text-decoration: none;
  outline: none;
}
.ve-dropdown .ve-dropdown-dt,
.ve-dropdown .ve-dropdown-items {
  margin: 0px;
  padding: 0px;
}
.ve-dropdown .ve-dropdown-dt-selected {
  position: relative;
  display: block;
  border: 1px solid #c8cdd4;
  border-radius: 2px;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
}
.ve-dropdown .ve-dropdown-dt-selected:hover {
  color: #108ee9;
  border-color: #108ee9;
}
.ve-dropdown .ve-dropdown-dt-selected .ve-dropdown-dt-selected-span {
  width: 80%;
  display: block !important;
  /*修复会被别的样式覆盖的问题*/
  text-align: center;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  padding-left: 2px;
}
.ve-dropdown .ve-dropdown-dt-selected .ve-dropdown-input {
  appearance: none;
  background-color: #fff;
  background-image: none;
  border: 1px solid #fff;
  box-sizing: border-box;
  color: #1f2d3d;
  display: inline-block;
  font-size: inherit;
  line-height: 1;
  outline: none;
  padding-left: 2px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  width: 80%;
  text-align: left;
}
.ve-dropdown-popper {
  z-index: 999;
  /*操作功能开始*/
}
.ve-dropdown-popper .ve-dropdown-dd,
.ve-dropdown-popper .ve-dropdown-items {
  margin: 0px;
  padding: 0px;
}
.ve-dropdown-popper .ve-dropdown-dd {
  display: block;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items {
  min-height: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: normal;
  white-space: nowrap;
  top: 2px;
  left: 0px;
  list-style: none;
  border-radius: 2px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  border: 1px solid #d1dbe5;
  padding: 5px 0px;
  width: auto;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper {
  overflow: auto;
  /* 单选 */
  /* 多选 */
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li {
  white-space: nowrap;
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  background-color: #fff;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li a {
  text-decoration: none;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li:hover {
  background-color: #f3f3f3;
  color: #108ee9;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li.active {
  background-color: #e6f7ff;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li.active a {
  color: #108ee9;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li.active:hover {
  background-color: #e6f7ff;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li .ve-dropdown-items-li-a {
  width: 100%;
  display: block;
  padding-left: 8px;
  padding-right: 8px;
  color: #000000d9;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li .ve-dropdown-items-li-a-left {
  text-align: left;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li .ve-dropdown-items-li-a-center {
  text-align: center;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-li .ve-dropdown-items-li-a-right {
  text-align: right;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-multiple {
  display: table;
  padding: 0 5px;
  width: 100%;
  text-align: left;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-multiple .ve-checkbox {
  width: 100%;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-multiple .ve-checkbox .ve-checkbox-label {
  padding-left: 5px;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-multiple span {
  font-size: 14px;
  font-weight: normal;
  color: #108ee9;
}
.ve-dropdown-popper .ve-dropdown-dd .ve-dropdown-items .ve-dropdown-items-warpper .ve-dropdown-items-multiple:hover {
  background-color: #f3f3f3;
}
.ve-dropdown-popper .ve-dropdown-operation {
  width: 100%;
  margin-top: 5px;
  padding: 8px 0 3px 0;
  font-size: 14px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-around;
}
.ve-dropdown-popper .ve-dropdown-operation .ve-dropdown-operation-item {
  color: #495060;
}
.ve-dropdown-popper .ve-dropdown-operation .ve-dropdown-operation-item.ve-dropdown-filter-disable {
  color: #999;
}
.ve-dropdown-popper .ve-dropdown-operation .ve-dropdown-operation-item:not(.ve-dropdown-filter-disable):hover {
  color: #108ee9;
}
.ve-dropdown-popper .ve-dropdown-operation:last-child {
  float: right;
}
.ve-dropdown-popper .ve-dropdown-operation:hover {
  color: #f3f3f3;
}
.ve-icon {
  display: inline-block;
}
.ve-loading-parent-relative {
  position: relative !important;
}
.ve-loading-parent-lock {
  overflow: hidden !important;
}
.ve-loading {
  /* plane */
  /* bounce */
  /* wave */
  /* pulse */
  /* flow */
  /* grid */
}
.ve-loading.ve-loading-overlay {
  position: absolute;
  z-index: 1999;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.ve-loading.ve-loading-fixed {
  position: fixed !important;
}
.ve-loading.ve-loading-hide {
  display: none;
}
.ve-loading-spin-container {
  position: absolute;
  top: 50%;
  margin-top: -20px;
  margin-left: -20px;
  width: 100%;
  text-align: center;
}
.ve-loading-spin-container .ve-loading-spin {
  display: inline-block;
}
.ve-loading-plane {
  animation: sk-plane 1.2s infinite ease-in-out;
}
@keyframes sk-plane {
  0% {
    transform: perspective(120px) rotateX(0deg) rotateY(0deg);
  }
  50% {
    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
  }
  100% {
    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
  }
}
.ve-loading-bounce {
  position: relative;
}
.ve-loading-bounce-dot {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  /* background-color: var(--sk-color); */
  opacity: 0.6;
  position: absolute;
  top: 0;
  left: 0;
  animation: sk-bounce 2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955);
}
.ve-loading-bounce-dot:nth-child(2) {
  animation-delay: -1s;
}
@keyframes sk-bounce {
  0%,
  100% {
    transform: scale(0);
  }
  45%,
  55% {
    transform: scale(1);
  }
}
.ve-loading-wave {
  display: flex;
  justify-content: space-between;
}
.ve-loading-wave-rect {
  height: 100%;
  width: 15%;
  animation: sk-wave 1.2s infinite ease-in-out;
}
.ve-loading-wave-rect:nth-child(1) {
  animation-delay: -1.2s;
}
.ve-loading-wave-rect:nth-child(2) {
  animation-delay: -1.1s;
}
.ve-loading-wave-rect:nth-child(3) {
  animation-delay: -1s;
}
.ve-loading-wave-rect:nth-child(4) {
  animation-delay: -0.9s;
}
.ve-loading-wave-rect:nth-child(5) {
  animation-delay: -0.8s;
}
@keyframes sk-wave {
  0%,
  40%,
  100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
.ve-loading-pulse {
  width: var(--sk-size);
  height: var(--sk-size);
  background-color: var(--sk-color);
  border-radius: 100%;
  animation: sk-pulse 1.2s infinite cubic-bezier(0.455, 0.03, 0.515, 0.955);
}
@keyframes sk-pulse {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
.ve-loading-flow {
  width: calc(var(--sk-size) * 1.3);
  height: calc(var(--sk-size) * 1.3);
  display: flex;
  justify-content: space-between;
}
.ve-loading-flow-dot {
  width: 25%;
  height: 25%;
  background-color: var(--sk-color);
  border-radius: 50%;
  animation: sk-flow 1.4s cubic-bezier(0.455, 0.03, 0.515, 0.955) 0s infinite both;
}
.ve-loading-flow-dot:nth-child(1) {
  animation-delay: -0.3s;
}
.ve-loading-flow-dot:nth-child(2) {
  animation-delay: -0.15s;
}
@keyframes sk-flow {
  0%,
  80%,
  100% {
    transform: scale(0.3);
  }
  40% {
    transform: scale(1);
  }
}
.ve-loading-grid {
  /* Cube positions
     * 1 2 3
     * 4 5 6
     * 7 8 9
     */
}
.ve-loading-grid-cube {
  width: 33.33%;
  height: 33.33%;
  background-color: var(--sk-color);
  float: left;
  animation: sk-grid 1.3s infinite ease-in-out;
}
.ve-loading-grid-cube:nth-child(1) {
  animation-delay: 0.2s;
}
.ve-loading-grid-cube:nth-child(2) {
  animation-delay: 0.3s;
}
.ve-loading-grid-cube:nth-child(3) {
  animation-delay: 0.4s;
}
.ve-loading-grid-cube:nth-child(4) {
  animation-delay: 0.1s;
}
.ve-loading-grid:nth-child(5) {
  animation-delay: 0.2s;
}
.ve-loading-grid-cube:nth-child(6) {
  animation-delay: 0.3s;
}
.ve-loading-grid-cube:nth-child(7) {
  animation-delay: 0s;
}
.ve-loading-grid-cube:nth-child(8) {
  animation-delay: 0.1s;
}
.ve-loading-grid-cube:nth-child(9) {
  animation-delay: 0.2s;
}
@keyframes sk-grid {
  0%,
  70%,
  100% {
    transform: scale3D(1, 1, 1);
  }
  35% {
    transform: scale3D(0, 0, 1);
  }
}
*,
:after,
:before {
  box-sizing: border-box;
}
.ve-pagination {
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  background-color: #fff;
  margin: 0;
  padding: 0;
  display: inline-block;
  margin: 0 4px;
  list-style-type: none;
}
.ve-pagination .ve-pagination-total {
  float: left;
  margin: 0 4px;
  color: #000000d9;
}
.ve-pagination .ve-pagination-select {
  float: left;
  margin: 0 4px;
}
.ve-pagination .ve-pagination-pager {
  float: left;
}
.ve-pagination .ve-pagination-goto {
  float: left;
  margin: 0 4px;
  color: #000000d9;
}

</style>
